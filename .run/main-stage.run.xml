<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="main-stage" type="FlutterRunConfigurationType" factoryName="Flutter">
    <option name="additionalArgs" value="--dart-define-from-file=.environment/env-stage.json" />
    <option name="attachArgs" value="--dart-define-from-file=.environment/env-stage.json" />
    <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
    <method v="2">
      <option name="ToolBeforeRunTask" enabled="true" actionId="Tool_mTools_Restrict Downgrade" />
    </method>
  </configuration>
</component>