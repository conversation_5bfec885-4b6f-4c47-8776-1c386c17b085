<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="main-beinex-dev" type="FlutterRunConfigurationType" factoryName="Flutter">
    <option name="additionalArgs" value="--dart-define-from-file=.environment/env-beinex-dev.json" />
    <option name="attachArgs" value="--dart-define-from-file=.environment/env-beinex-dev.json" />
    <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
    <method v="2" />
  </configuration>
</component>