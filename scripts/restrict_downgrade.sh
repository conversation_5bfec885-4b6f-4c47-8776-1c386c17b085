#!/bin/bash

PACKAGE_NAME="ae.gov.scad.adstatapp"  # Change this to your app's package name

# Extract versionName and versionCode from pubspec.yaml
VERSION_LINE=$(grep -m 1 "version:" pubspec.yaml | awk '{print $2}')
VERSION_NAME=$(echo $VERSION_LINE | cut -d "+" -f1)
VERSION_CODE=$(echo $VERSION_LINE | cut -d "+" -f2)

if [ -z "$VERSION_NAME" ] || [ -z "$VERSION_CODE" ]; then
    echo "❌ Error: Unable to fetch version from pubspec.yaml"
    exit 1
fi

echo "📦 Checking installed version for $PACKAGE_NAME..."

# Get the installed versionName and versionCode from the emulator
INSTALLED_VERSION_NAME=$(adb shell dumpsys package $PACKAGE_NAME | grep versionName | awk -F= '{print $2}')
INSTALLED_VERSION_CODE=$(adb shell dumpsys package $PACKAGE_NAME | grep versionCode | awk '{print $1}' | awk -F= '{print $2}')

if [ -z "$INSTALLED_VERSION_NAME" ] || [ -z "$INSTALLED_VERSION_CODE" ]; then
    echo "✅ No existing version found. Proceeding with install."
    exit 0
fi

echo "📌 Installed version : $INSTALLED_VERSION_NAME+$INSTALLED_VERSION_CODE"
echo "🚀 Version to Install: $VERSION_NAME+$VERSION_CODE"

# Function to compare semantic versions
version_compare() {
    printf '%s\n%s\n' "$1" "$2" | sort -V | head -n1
}

# Compare versionName and versionCode
if [[ "$INSTALLED_VERSION_CODE" -gt "$VERSION_CODE" ]]; then
    echo "⚠️ Installed version ($INSTALLED_VERSION_NAME+$INSTALLED_VERSION_CODE) is greater than new version ($VERSION_NAME+$VERSION_CODE)"
    echo "⚠️ This will uninstall the app causing data loss."
    echo "🛑 Stopping the process"
    exit 1
else
    echo "🔄 Installed version is same or lower. Proceeding with installation..."
fi