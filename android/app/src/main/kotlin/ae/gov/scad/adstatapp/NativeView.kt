package ae.gov.scad.adstatapp

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.BitmapDrawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.arcgismaps.ArcGISEnvironment
import com.arcgismaps.Color
import com.arcgismaps.LicenseKey
import com.arcgismaps.LicenseResult
import com.arcgismaps.data.Feature
import com.arcgismaps.data.FeatureQueryResult
import com.arcgismaps.data.FeatureTable
import com.arcgismaps.data.QueryFeatureFields
import com.arcgismaps.data.QueryParameters
import com.arcgismaps.data.ServiceFeatureTable
import com.arcgismaps.geometry.AreaUnit
import com.arcgismaps.geometry.GeodeticCurveType
import com.arcgismaps.geometry.Geometry
import com.arcgismaps.geometry.GeometryBuilder
import com.arcgismaps.geometry.GeometryEngine
import com.arcgismaps.geometry.GeometryType
import com.arcgismaps.geometry.Multipoint
import com.arcgismaps.geometry.Point
import com.arcgismaps.geometry.Polygon
import com.arcgismaps.geometry.Polyline
import com.arcgismaps.geometry.SpatialReference
import com.arcgismaps.httpcore.authentication.ArcGISAuthenticationChallengeHandler
import com.arcgismaps.httpcore.authentication.ArcGISAuthenticationChallengeResponse
import com.arcgismaps.httpcore.authentication.PregeneratedTokenCredential
import com.arcgismaps.httpcore.authentication.TokenInfo
import com.arcgismaps.internal.jni.CoreArcGISRuntimeEnvironment
import com.arcgismaps.mapping.ArcGISMap
import com.arcgismaps.mapping.PortalItem
import com.arcgismaps.mapping.Viewpoint
import com.arcgismaps.mapping.layers.FeatureLayer
import com.arcgismaps.mapping.layers.Layer
import com.arcgismaps.mapping.symbology.PictureMarkerSymbol
import com.arcgismaps.mapping.symbology.Renderer
import com.arcgismaps.mapping.symbology.SimpleFillSymbol
import com.arcgismaps.mapping.symbology.SimpleFillSymbolStyle
import com.arcgismaps.mapping.symbology.SimpleLineSymbol
import com.arcgismaps.mapping.symbology.SimpleLineSymbolStyle
import com.arcgismaps.mapping.symbology.SimpleMarkerSymbol
import com.arcgismaps.mapping.symbology.SimpleMarkerSymbolStyle
import com.arcgismaps.mapping.symbology.SimpleRenderer
import com.arcgismaps.mapping.symbology.UniqueValue
import com.arcgismaps.mapping.symbology.UniqueValueRenderer
import com.arcgismaps.mapping.view.Graphic
import com.arcgismaps.mapping.view.GraphicsOverlay
import com.arcgismaps.mapping.view.MapView
import com.arcgismaps.mapping.view.ScreenCoordinate
import com.arcgismaps.mapping.view.geometryeditor.GeometryEditor
import com.arcgismaps.mapping.view.geometryeditor.VertexTool
import com.arcgismaps.portal.Portal
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.StandardMessageCodec
import io.flutter.plugin.platform.PlatformView
import io.flutter.plugin.platform.PlatformViewFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Timer
import kotlin.concurrent.timerTask


internal class NativeView(
    context: Context, id: Int, creationParams: Map<String?, Any?>?, flutterEngine: FlutterEngine
) : PlatformView {

    private val messageKey = "message_key"

    private val outMessageKeyValueMapReady = "map_ready"
    private val outMessageKeyValueApiResponse = "api_response"
    private val outMessageKeyValueDistrictSelected = "district_selected"
    private val outMessageKeyValueMeasurementResult = "measurement_result"
    private val outMessageKeyValueSelectionDone = "selection_done"
    private val outMessageKeyValueToast = "toast_message"

    private val inMessageKeyValueInitMap = "init_map"
    private val inMessageKeyValueModuleChanged = "module_changed"
    private val inMessageKeyValueUpdateLayer = "update_layer"
    private val inMessageKeyValuePOI = "point_of_interest"
    private val inMessageKeyValueMeasurement = "measurement"
    private val inMessageKeyValueSelection = "selection"
    private val inMessageKeyValueClearMapSelection = "clear_map_selection"
    private val inMessageKeyValueHasSomethingToClearOnMap = "has_something_to_clear_on_map"

    private val eventChannelName = "arcgisMapEvent"
    private val methodChannelName = "arcgisMapMethod"

    private val keyJobSeekers: String = "1"
    private val keyHouseHold: String = "2"
    private val keyCensus = "3"
    private val keyPopulationByRegion: String = "4"
    private val keyRealEstateFlatTransaction = "5"
    private val keyRealEstateCensus = "6"

    private var eventHandler: EventHandler = EventHandler
    private var districtFeatureLayer: FeatureLayer? = null
    private lateinit var mapView: MapView

    private lateinit var graphicsOverlay: GraphicsOverlay
    private lateinit var geometryEditor: GeometryEditor
    private lateinit var vertexTool: VertexTool

    // create a symbol for the point graphic
    private val pointSymbol: SimpleMarkerSymbol by lazy {
        SimpleMarkerSymbol(
            SimpleMarkerSymbolStyle.Square,
            Color.green,
            20f
        )
    }

    // create a symbol for a line graphic
    private val lineSymbol: SimpleLineSymbol by lazy {
        SimpleLineSymbol(
            SimpleLineSymbolStyle.Solid,
            Color.red,
            2f
        )
    }

    // create a symbol for the fill graphic
    private val fillSymbol: SimpleFillSymbol by lazy {
        SimpleFillSymbol(
            SimpleFillSymbolStyle.Solid,
            Color.transparent,
            lineSymbol
        )
    }

    private val lifecycleScope: CoroutineScope
        get() = MainActivity.getInstance().lifecycleScope

    private lateinit var view: View

    private var activeMapLayerList: ArrayList<String> = ArrayList()

    override fun getView(): View {
        return view
    }

    override fun dispose() {
        MainActivity.getInstance().lifecycle.removeObserver(mapView)
    }

    init {
        try {
            EventChannel(
                flutterEngine.dartExecutor.binaryMessenger, eventChannelName
            ).setStreamHandler(EventHandler)

            MethodChannel(
                flutterEngine.dartExecutor.binaryMessenger, methodChannelName
            ).setMethodCallHandler { call, result ->
                handleIncomingMessage(call, result )
            }

            //  This is a fix for crash in Android. The temp file creation fails in arcgis sdk,
            //  calling this somehow ensures cache dir is present.
            val cacheDir = context.cacheDir
            CoreArcGISRuntimeEnvironment.setTempDirectory(cacheDir.path)
            //  ---- END OF FIX ----

            applyLicense(creationParams?.get("licenseString").toString())

            val baseMapPortalId = creationParams?.get("baseMapPortalId").toString()
            val accessToken = creationParams?.get("token").toString()

            ArcGISEnvironment.authenticationManager.arcGISAuthenticationChallengeHandler =
                ArcGISAuthenticationChallengeHandler { challenge ->
                    val tokenInfo =
                        TokenInfo(accessToken, Instant.now().plus(8, ChronoUnit.HOURS), true)
                    val preGeneratedTokenCredential =
                        PregeneratedTokenCredential(challenge.requestUrl, tokenInfo)
                    ArcGISAuthenticationChallengeResponse.ContinueWithCredential(
                        preGeneratedTokenCredential
                    )
                }

            graphicsOverlay= GraphicsOverlay()
            geometryEditor= GeometryEditor()
            vertexTool= VertexTool()

            val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            view = inflater.inflate(R.layout.activity_main, null)

            mapView = view.findViewById(R.id.mapView)

            mapView.apply {
                map = ArcGISMap(
                    PortalItem(
                        Portal("https://www.arcgis.com", Portal.Connection.Anonymous),
                        baseMapPortalId
                    )
                )
                graphicsOverlays.add(graphicsOverlay)
                selectionProperties.color = Color.green
            }

            // set MapView's geometry editor to sketch on map
            mapView.geometryEditor = geometryEditor
            MainActivity.getInstance().lifecycle.addObserver(mapView)
            mapView.setViewpoint(Viewpoint(24.43, 54.37, 179757.5))

            Timer().schedule(timerTask {
                sendMessageToFlutter(
                    outMessageKeyValueMapReady, null
                )
            }, 100)
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to initialize map","error")
        }
    }

    fun applyLicense(licenseString: String) {
        try {
            val licenseKey = LicenseKey.create(licenseString)!!
            val licenseResult: LicenseResult? =
                ArcGISEnvironment.setLicense(licenseKey, listOf<LicenseKey>())
        } catch (e: Exception) {
            e.printStackTrace()
            toast("Unable to apply license", "error")
        }
    }

    private fun sendMessageToFlutter(messageType: String, dataHashMap: HashMap<String, Any>?) {
        try {
            val hashMap: HashMap<String, Any> = HashMap<String, Any>().apply {
                this[messageKey] = messageType
            }
            if (dataHashMap != null) {
                hashMap.putAll(dataHashMap)
            }

            Log.d("TAG", "sendMessageToFlutter: $hashMap")

            MainActivity.getInstance().runOnUiThread {
                try {
                    eventHandler.eventSink?.success(hashMap)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun handleIncomingMessage(call: MethodCall, result: MethodChannel.Result) {

        Log.d("TAG", "handleIncomingMessage: method:${call.method}\narguments:${call.arguments}")
        try {
            when (call.method) {
//                inMessageKeyValueInitMap -> {
//                val arguments = call.arguments as HashMap<String, Any>
//                if (arguments.containsKey("district_layer")) {
//                    lifecycleScope.launch {
//                        loadLayerDistrict(arguments["district_layer"].toString())
//                    }
//                }
//                }

                inMessageKeyValueModuleChanged -> {
                    districtFeatureLayer?.clearSelection()
                    val module: String? = call.argument<String>("moduleKey")

                    val layerUrls: HashMap<String, String>? = call.argument("layerUrls")
                    val districtLayerUrl: String? = call.argument("districtLayer")

                    when (module) {
                        keyCensus -> {
//                        val dotDensityLayerUrl: String? = layerUrls?.get("dotDensityLayerUrl")
                            val populationLayerUrl: String? = layerUrls?.get("populationLayerUrl")

                            try {
                                lifecycleScope.launch {
                                    loadLayerDistrict(districtLayerUrl)
                                }
                            } catch (e: Exception) {
                                toast(e.message ?: "Unable to load layer", "error")
                                e.printStackTrace()
                            }

                            try {
                                lifecycleScope.launch {
                                    loadHouseholdPopulationLayer(populationLayerUrl)
                                }
                            } catch (e: Exception) {
                                toast(e.message ?: "Unable to load layer", "error")
                                e.printStackTrace()
                            }
                        }

                        keyHouseHold -> {
                            val populationLayerUrl: String? = layerUrls?.get("populationLayerUrl")

                                try {
                                    lifecycleScope.launch {
                                        loadLayerDistrict(districtLayerUrl)
                                    }  } catch (e: Exception) {
                                    toast(e.message ?: "Unable to load layer", "error")
                                    e.printStackTrace()
                                }

                                try {
                                    lifecycleScope.launch {
                                        loadHouseholdPopulationLayer(populationLayerUrl)
                                    }
                                } catch (e: Exception) {
                                    toast(e.message ?: "Unable to load layer", "error")
                                    e.printStackTrace()
                                }
                        }

                        keyRealEstateFlatTransaction -> {

                                try {
                                    lifecycleScope.launch {
                                        loadLayerDistrict(districtLayerUrl)
                                    }
                                } catch (e: Exception) {
                                    toast(e.message ?: "Unable to load layer", "error")
                                    e.printStackTrace()
                                }

//                        val realEstateDataLayerUrl: String? =
//                            layerUrls?.get("realEstateDataLayerUrl")
//
//                        loadRealEstateDataLayer(realEstateDataLayerUrl)
                        }

                        keyJobSeekers -> {

                            val greyShadeLayerUrl: String? = layerUrls?.get("greyShadeLayerUrl")

                                try {
                                    lifecycleScope.launch {
                                        loadLayerDistrict(districtLayerUrl)
                                    }    } catch (e: Exception) {
                                    toast(e.message ?: "Unable to load layer", "error")
                                    e.printStackTrace()
                                }

                                try {
                                    lifecycleScope.launch {
                                        loadLayer("greyShadeLayerUrl", greyShadeLayerUrl)
                                    }   } catch (e: Exception) {
                                    toast(e.message ?: "Unable to load layer", "error")
                                    e.printStackTrace()
                                }
                        }

                        keyRealEstateCensus -> {
                            val realEstateBuildingsOnlyForVisualization: String? = layerUrls?.get("realEstateBuildingsOnlyForVisualization")

                            try {
                                lifecycleScope.launch {
                                    loadLayerDistrict(districtLayerUrl)
                                }    } catch (e: Exception) {
                                toast(e.message ?: "Unable to load layer", "error")
                                e.printStackTrace()
                            }

                            try {
                                val layerRenderer = SimpleRenderer(
                                    SimpleMarkerSymbol(
                                        SimpleMarkerSymbolStyle.Circle,
                                        Color.black, //(android.graphics.Color.parseColor("#6A7180")),
                                        5f
                                    )
                                )

                                lifecycleScope.launch {
                                    loadLayer("realEstateBuildingsOnlyForVisualization", realEstateBuildingsOnlyForVisualization, layerRenderer)
                                }
                            } catch (e: Exception) {
                                toast(e.message ?: "Unable to load layer", "error")
                                e.printStackTrace()
                            }
                        }

                        else -> {}
                    }
                }

                inMessageKeyValueUpdateLayer -> {
                    val module: String? = call.argument<String>("moduleKey")
                    if (module == keyHouseHold) {
                        val data: HashMap<String, Any> = call.argument("data")!!

                        val showNonUae = data["showNonUae"] as Boolean
                        val showUae = data["showUae"] as Boolean

                        householdPopulationLayerUpdate(showNonUae = showNonUae, showUae = showUae)
                    }
                }

                inMessageKeyValuePOI -> {
                    val data: List<HashMap<String, Any>> = call.argument("poi")!!

                    clearPoiLayers()

                    for (poi in data) {
                        val title = poi["title"] as String
                        val url = poi["url"] as String
                        val filePath = poi["filePath"] as String

                        loadPOILayer(title, url, filePath)
                    }
                }

                inMessageKeyValueMeasurement -> {
                    val event: String? = call.argument<String>("event")
                    val type: String? = call.argument<String>("type")

                    when (event) {
                        "start" -> {
                            startMeasurement(type)
                        }
                        "stop" -> {
                            stopMeasurement(type ?: "")
                        }
                        "cancel" -> {
                            cancelSketch()
                        }
                        "clear" -> {
                            clearMeasurement()
                        }
                    }
                }

                inMessageKeyValueSelection -> {

                    when (call.argument<String>("event")) {
                        "start" -> {
                            startSketch()
                        }
                        "done" -> {
                            stopSketch()
                        }
                        "cancel" -> {
                            cancelSketch()
                        }
                        "undo" -> {
                            undoSketch()
                        }
                        "redo" -> {
                            redoSketch()
                        }
                    }
                }

                inMessageKeyValueClearMapSelection -> {
                    clearMapSelection()
                }
                inMessageKeyValueHasSomethingToClearOnMap -> {
                    result.success(graphicsOverlay.graphics.isNotEmpty())
                    return
                }

                else -> {
                    Log.d("TAG", "invalid")
                }
            }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to complete the operation","error")
        }
        result.success(true)
    }

    private fun parseColor(colorHexCode: String) : Color{
        return Color(android.graphics.Color.parseColor(colorHexCode))
    }

    private fun toast(message: String, type: String = "success") {
        sendMessageToFlutter(outMessageKeyValueToast,
            HashMap<String, Any>().apply {
                this["message"] = message
                this["type"] = type
            })
    }

    private fun addMapLayer(title: String, element: Layer) {
        try {
            if (title == "district") {
                activeMapLayerList.add(0, title)
                mapView.map?.operationalLayers?.add(0, element)
            } else {
                activeMapLayerList.add(title)
                mapView.map?.operationalLayers?.add(element)
            }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to add map layer","error")
        }
//        lifecycleScope.launch {
//            mapView.setViewpointScale(mapView.mapScale.value + 5)
//            mapView.requestFocus()
//        }
    }

    private fun removeMapLayer(title: String) {
        try {
            val index = activeMapLayerList.indexOf(title)
            activeMapLayerList.removeAt(index)
            mapView.map?.operationalLayers?.removeAt(index)
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to remove map layer","error")
        }
    }

    private suspend fun loadLayerDistrict(url: String?) {
        try {
            if (url == null) return

            clearMapLayers()
            val layerRenderer = SimpleRenderer(
                SimpleFillSymbol(
                    SimpleFillSymbolStyle.Solid,
                    parseColor("#33808080"),//Color.transparent,
                    SimpleLineSymbol(
                        SimpleLineSymbolStyle.Solid, parseColor("#808080"), 1f
                    )
                )
            )


            val serviceFeatureTable = ServiceFeatureTable(url)

            val queryParameters = QueryParameters().apply {
                whereClause = ("1=1")
            }

            val featureQueryResult: FeatureQueryResult? = serviceFeatureTable.queryFeatures(
                queryParameters, QueryFeatureFields.LoadAll
            ).getOrNull()

            if (featureQueryResult == null) {
                toast("Unable to load map data", "error")
                Log.d("TAG", "loadDistricts: featureQueryResult == null")
            } else {
                val feature = featureQueryResult.firstOrNull()

                val featureTable: FeatureTable? = feature?.featureTable
                if (featureTable == null) {
                    toast("Unable to load map data", "error")
                    Log.d("TAG", "loadDistrict: featureTable is null")
                } else {
                    districtFeatureLayer = FeatureLayer.createWithFeatureTable(featureTable).apply {
                        renderer = layerRenderer
                    }

                    addMapLayer("district", districtFeatureLayer!!)

                    sendMessageToFlutter(outMessageKeyValueApiResponse,
                        HashMap<String, Any>().apply {
                            this["key"] = "district_list"
                            this["url"] = url
                            this["attributes"] =
                                JSONArray(featureQueryResult.map { v -> v.attributes }
                                    .toList()).toString()
                        })

                    mapView.onSingleTapConfirmed.collect { tapEvent ->
                        if (!geometryEditor.isStarted.value) {
                            val screenCoordinate = tapEvent.screenCoordinate
                            getSelectedFeatureLayer(screenCoordinate)
                        }
                    }
                }
            }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to load district layer","error")
        }
    }

    private suspend fun loadLayer(title: String, url: String?, renderer: Renderer? = null) {
        try {
            if (url == null) return
            val featureTable: FeatureTable? = query(url)

            if (featureTable == null) {
                Log.d("TAG", "loadLayer: featureTable is null")
            } else {
                val featureLayer: FeatureLayer = FeatureLayer.createWithFeatureTable(featureTable)
                featureLayer.renderer = renderer

                addMapLayer(title, featureLayer)
            }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to load map layer","error")
        }
    }

    fun openFileAsBitmap(filePath: String): Bitmap? {
        val options = BitmapFactory.Options()
        // Only decode bounds information initially to check if file exists and avoid memory allocation for large images
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(filePath, options)

//        // Check if file exists based on options
//        if (!options.outWidth.orZero() > 0 || !options.outHeight.orZero() > 0) {
//            return null // File not found or invalid
//        }

        // Set appropriate inSampleSize for memory efficiency based on desired image size
        // You can adjust this logic based on your needs (e.g., target display size)
        options.inSampleSize = calculateInSampleSize(options, 500, 500) // Target max width/height 500px

        // Decode the bitmap with the inSampleSize for memory optimization
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeFile(filePath, options)
    }

    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val (height, width) = options.outHeight to options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }

    private fun loadPOILayer(title: String, url: String, filePath: String) {
        try {
            if (!activeMapLayerList.contains(title)) {

                val featureTable1 = ServiceFeatureTable(url)
                val featureLayer = FeatureLayer.createWithFeatureTable(featureTable1)

            featureLayer.renderer = SimpleRenderer(
                PictureMarkerSymbol.createWithImage(
                    BitmapDrawable(null, openFileAsBitmap(filePath))).apply {
                    height = 18.0f
                    width = 18.0f
                }
            )

//                featureLayer.renderer = SimpleRenderer(PictureMarkerSymbol(imgUrl).apply {
//                    height = 18.0f
//                    width = 18.0f
//                })

                addMapLayer(title, featureLayer)


//                val featureTable: FeatureTable? = query(url)
//
//                if (featureTable == null) {
//                    Log.d("TAG", "loadPOILayer: featureTable is null")
//                } else {
//                    val featureLayer: FeatureLayer =
//                        FeatureLayer.createWithFeatureTable(featureTable)
//                    addMapLayer(title, featureLayer)
//                }
            }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to load poi layer","error")
        }
    }

    private suspend fun query(url: String): FeatureTable? {
        try {
            var whereCondition = "1=1"

            val serviceFeatureTable = ServiceFeatureTable(url)

            val queryParameters = QueryParameters().apply {
                whereClause = (whereCondition)
            }

            val featureQueryResult: FeatureQueryResult? = serviceFeatureTable.queryFeatures(
                queryParameters, QueryFeatureFields.LoadAll
            ).getOrNull()

            if (featureQueryResult == null) {
                Log.d("TAG", "query: featureQueryResult == null")
                return null
            }

            val feature = featureQueryResult.firstOrNull()

            sendMessageToFlutter(outMessageKeyValueApiResponse, HashMap<String, Any>().apply {
                this["url"] = url
                this["attributes"] =
                    JSONArray(featureQueryResult.map { v -> v.attributes }.toList()).toString()
            })

            return feature?.featureTable
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to query","error")
        }
        return null
    }

    private suspend fun getFeatureQueryResult(
        url: String, whereCondition: String = "1=1"
    ): FeatureQueryResult? {
        try {
            val serviceFeatureTable = ServiceFeatureTable(url)

            val queryParameters = QueryParameters().apply {
                whereClause = (whereCondition)
            }

            return serviceFeatureTable.queryFeatures(
                queryParameters, QueryFeatureFields.LoadAll
            ).getOrThrow()
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to get Feature Query Result","error")
        }
        return null
    }

    private fun clearPoiLayers() {
        try {
            val tempList: List<String> = activeMapLayerList.filter { e -> e.startsWith("poi-") }

            for (i in tempList.size - 1 downTo 0) {
                val index = activeMapLayerList.indexOf(tempList[i])
                mapView.map?.operationalLayers?.removeAt(index)
            }

            activeMapLayerList.removeIf { e -> e.startsWith("poi-") }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to clear Poi Layers","error")
        }
    }

    private fun clearMapLayers() {
        try {
            if (mapView.map?.operationalLayers?.isNotEmpty() == true) {
//            val index = activeMapLayerList.indexOf("district")
//            val layer = mapView.map?.operationalLayers?.get(index)

                mapView.map?.operationalLayers?.clear()
                activeMapLayerList.clear()

//            addMapLayer("district", layer!!)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            toast("Unable to clear Map Layers", "error")
        }
    }

    private suspend fun getSelectedFeatureLayer(screenCoordinate: ScreenCoordinate) {
        try { // clear the previous selection
            districtFeatureLayer?.clearSelection()
            // set a tolerance for accuracy of returned selections from point tapped
            val tolerance = 0.0
            // create a IdentifyLayerResult using the screen coordinate
            val identifyLayerResult =
                mapView.identifyLayer(
                    districtFeatureLayer!!,
                    screenCoordinate,
                    tolerance,
                    false,
                    -1
                )
            // handle the result's onSuccess and onFailure
            identifyLayerResult.apply {
                onSuccess { identifyLayerResult ->
                    try {
                        // get the elements in the selection that are features
                        val features = identifyLayerResult.geoElements.filterIsInstance<Feature>()
                        // add the features to the current feature layer selection
                        districtFeatureLayer!!.selectFeatures(features)
//                        mapView.setViewpointScale(mapView.mapScale.value + 0.0000000000000001)

                        sendMessageToFlutter(
                            outMessageKeyValueDistrictSelected,
                            HashMap<String, Any>().apply {
                                this["attributes"] = JSONObject(
                                    features.firstOrNull()?.attributes ?: HashMap<String, Any>()
                                ).toString()
                            })
                    }catch (e:Exception){
                        e.printStackTrace()
                        toast("Unable to select map layer","error")
                    }
                }
                onFailure {
                    it.printStackTrace()
                    toast("Select feature failed: " + it.message, "error")
                }
            }
        } catch (e:Exception){
            e.printStackTrace()
            toast("Unable to select map layer","error")
        }
    }

    private fun clearMapSelection(){
        districtFeatureLayer!!.clearSelection()
        cancelSketch()
    }

    private fun startSketch() {
        startGeometry(GeometryType.Polygon)
    }

    private fun stopSketch() {
        val sketchGeometry: Geometry? = stopGeometry()
        if (sketchGeometry != null) {
            sendMessageToFlutter(outMessageKeyValueSelectionDone, HashMap<String, Any>().apply {
                this["geometry"] = sketchGeometry.toJson()
            })
        }
    }

    private fun startMeasurement(type: String?) {
        if (type == "area") {
            startGeometry(GeometryType.Polygon)
        } else if (type == "distance") {
            startGeometry(GeometryType.Polyline)
        }
    }

    private fun startGeometry(geometryType: GeometryType) {
        geometryEditor.apply {
            tool = vertexTool

            start(geometryType)
        }
    }

    private fun stopMeasurement(type: String) {
        val sketchGeometry: Geometry? = stopGeometry()
        if (sketchGeometry != null) {
            measure(sketchGeometry, type)
        }
    }

    private fun stopGeometry(): Geometry? {

        val sketchGeometry = geometryEditor.geometry.value

        if (sketchGeometry == null) {
            toast("Error retrieving geometry","error")
            return null
        }

        if (!GeometryBuilder.builder(sketchGeometry).isSketchValid) {
            toast("Not a valid selection","error")
            geometryEditor.stop()
            return null
        }

        geometryEditor.stop()

        // create a graphic from the sketch editor geometry
        val graphic = Graphic(sketchGeometry).apply {
            // assign a symbol based on geometry type
            symbol = when (sketchGeometry) {
                is Polygon -> fillSymbol
                is Polyline -> lineSymbol
                is Point, is Multipoint -> pointSymbol
                else -> null
            }
        }

        // add the graphic to the graphics overlay
        graphicsOverlay.graphics.add(graphic)

        return sketchGeometry
    }

    private fun measure(sketchGeometry: Geometry, type: String) {
        if (type == "distance") {
            val list = extractList(JSONObject(sketchGeometry.toJson()).getJSONArray("paths"))

            var distance = 0.0
            for (i in 0 until list.size - 1) {
                val map1: HashMap<String, Double> = list[i]
                val map2: HashMap<String, Double> = list[i + 1]
                distance += measureDistance(map1["x"]!!, map1["y"]!!, map2["x"]!!, map2["y"]!!)
            }
            Log.d("TAG", "distance:$distance")

            sendMessageToFlutter(outMessageKeyValueMeasurementResult, HashMap<String, Any>().apply {
                this["type"] = type
                this["distance"] = distance
            })

        } else if (type == "area") {
            val area: Double =
                GeometryEngine.areaGeodetic(
                    sketchGeometry,
                    AreaUnit.squareMeters,
                    GeodeticCurveType.Geodesic
                )
            Log.d("TAG", "area: $area")

            val distance: Double =
                GeometryEngine.length(sketchGeometry)
            Log.d("TAG", "distance: $distance")

            sendMessageToFlutter(outMessageKeyValueMeasurementResult, HashMap<String, Any>().apply {
                this["type"] = type
                this["distance"] = distance
                this["area"] = area
            })
        }
    }

    private fun extractList(data: JSONArray): ArrayList<HashMap<String, Double>> {
        return if (data.length() == 1) {
            extractList(data[0] as JSONArray)
        } else {
            val list: ArrayList<HashMap<String, Double>> = ArrayList()
            for (i in 0 until data.length()) {
                list.add(HashMap<String, Double>().apply {
                    this["x"] = (data.get(i) as JSONArray).get(0).toString().toDouble()
                    this["y"] = (data.get(i) as JSONArray).get(1).toString().toDouble()
                })
            }
            list
        }
    }

    private fun clearMeasurement() {
        graphicsOverlay.graphics.clear()
        geometryEditor.clearGeometry()
        geometryEditor.clearSelection()
    }

    private fun cancelSketch() {
        graphicsOverlay.graphics.clear()
        geometryEditor.clearGeometry()
        geometryEditor.clearSelection()
        geometryEditor.stop()
    }

    private fun undoSketch() {
        if(geometryEditor.canUndo.value) {
            geometryEditor.undo()
        } else {
            toast("Cannot undo","error")
        }
    }

    private fun redoSketch() {
        if(geometryEditor.canRedo.value) {
            geometryEditor.redo()
        } else {
            toast("Cannot redo","error")
        }
    }

    private fun measureDistance(x1: Double, y1: Double, x2: Double, y2: Double): Double {

        val point1 = Point(x1, y1, 0.0, SpatialReference.wgs84())
        val point2 = Point(x2, y2, 0.0, SpatialReference.wgs84())

        // Create a world equidistant cylindrical spatial reference for measuring planar distance.
        val equidistantSpatialRef = SpatialReference.wgs84()

        // Project the points from geographic to the projected coordinate system.
        val edinburghProjected = GeometryEngine.projectOrNull(point1, equidistantSpatialRef)

        val darEsSalaamProjected = GeometryEngine.projectOrNull(point2, equidistantSpatialRef)

        if (edinburghProjected == null || darEsSalaamProjected == null) {
            return 0.0
        }

        // Get the planar distance between the points in the spatial reference unit (meters).
//        val planarDistanceMeters = GeometryEngine.distanceOrNull(edinburghProjected, darEsSalaamProjected)

        val planarDistanceMeters =
            GeometryEngine.distanceOrNull(edinburghProjected, darEsSalaamProjected)


        Log.d("TAG", "measureDistance: $planarDistanceMeters")
        return (planarDistanceMeters ?: 0.0)

    }

    private suspend fun loadHouseholdPopulationLayer(populationLayerUrl: String?) {
        try {
            if (populationLayerUrl == null) return

            clearMapLayers()

            val featureQueryResult: FeatureQueryResult? =
                getFeatureQueryResult(populationLayerUrl)

            val feature = featureQueryResult?.firstOrNull()

//                    sendMessageToFlutter(outMessageKeyValueApiResponse, HashMap<String, Any>().apply {
//                        this["url"] = populationLayerUrl!!
//                        this["attributes"] =
//                            JSONArray(featureQueryResult.map { v -> v.attributes }.toList()).toString()
//                    })

            if (feature?.featureTable == null) {
                Log.d("TAG", "loadHouseholdPopulationLayer: featureTable is null")
            } else {
                val featureLayer: FeatureLayer =
                    FeatureLayer.createWithFeatureTable(feature.featureTable!!)
                householdPopulationLayerAdd(
                    featureLayer = featureLayer, showNonUae = true, showUae = true
                )
            }
        } catch (e: Exception) {
            toast(e.message ?: "Unable to load layer","error")
            e.printStackTrace()
        }
    }

    private fun householdPopulationLayerAdd(
        featureLayer: FeatureLayer, showNonUae: Boolean, showUae: Boolean
    ) {
        try {
            val symbolNonUae = SimpleMarkerSymbol(
                SimpleMarkerSymbolStyle.Circle,
                if (showNonUae) parseColor("#594ede") else Color.transparent,
                4f
            )
            val symbolUae = SimpleMarkerSymbol(
                SimpleMarkerSymbolStyle.Circle,
                if (showUae) parseColor("#d75e5d") else Color.transparent,
                4f
            )

            val uniqueValueRenderer = UniqueValueRenderer(
                fieldNames = mutableListOf("premise_citizenship")
            ).apply {
                uniqueValues.addAll(
                    listOf(
                        UniqueValue(
                            description = "premise_citizenship",
                            label = "premise_citizenship",
                            values = listOf("NON_UAE"),
                            symbol = symbolNonUae
                        ),
                        UniqueValue(
                            description = "premise_citizenship",
                            label = "premise_citizenship",
                            values = listOf("UAE"),
                            symbol = symbolUae
                        ),
                    )
                )
            }

            featureLayer.renderer = uniqueValueRenderer

            addMapLayer("householdPopulation", featureLayer)
        } catch (e: Exception) {
            toast(e.message ?: "Unable to load layer","error")
            e.printStackTrace()
        }
    }

    private fun householdPopulationLayerUpdate(showNonUae: Boolean, showUae: Boolean) {
        try {
            val index: Int = activeMapLayerList.indexOf("householdPopulation")
            val featureLayer = mapView.map?.operationalLayers?.get(index) as FeatureLayer

            removeMapLayer(activeMapLayerList[index])

            householdPopulationLayerAdd(
                featureLayer = featureLayer, showNonUae = showNonUae, showUae = showUae
            )
        }catch (e: Exception) {
            toast(e.message ?: "Unable to update layer","error")
            e.printStackTrace()
        }
    }

    object EventHandler : EventChannel.StreamHandler {
        var eventSink: EventChannel.EventSink? = null

        override fun onListen(p0: Any?, sink: EventChannel.EventSink) {
            eventSink = sink
        }

        override fun onCancel(p0: Any?) {
            eventSink = null
        }
    }

}

class NativeViewFactory(private var flutterEngine: FlutterEngine) :
    PlatformViewFactory(StandardMessageCodec.INSTANCE) {
    override fun create(context: Context, viewId: Int, args: Any?): PlatformView {
        val creationParams = args as Map<String?, Any?>?
        return NativeView(context, viewId, creationParams, flutterEngine)
    }
}



