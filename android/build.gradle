allprojects {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven {
            url 'https://esri.jfrog.io/artifactory/arcgis'
        }
    }
}

apply from: 'sdk_properties.gradle'

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
