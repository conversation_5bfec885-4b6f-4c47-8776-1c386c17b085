// Copyright 2018 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


plugins {
    id 'com.android.library'
}

android {
    namespace "com.google.firebase.installations.interop"
    compileSdkVersion project.compileSdkVersion
    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion
        versionName version
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
     }
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
}

dependencies {
    api 'com.google.firebase:firebase-annotations:16.2.0'

    implementation 'com.google.android.gms:play-services-tasks:18.0.1'

    compileOnly "com.google.auto.value:auto-value-annotations:1.6.5"

    annotationProcessor "com.google.auto.value:auto-value:1.6.2"
}
