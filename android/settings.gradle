pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.6.1" apply false
    id "org.jetbrains.kotlin.android" version "2.1.0" apply false
    id "com.google.gms.google-services" version "4.4.2" apply false
}

dependencyResolutionManagement {
    versionCatalogs {
        libs {
            from(files("libs.versions.toml"))
        }
    }
}

include ":app"

include ":firebase-installations"
project(":firebase-installations").projectDir = new File(rootProject.projectDir, "local-libs/firebase-installations")
project(":firebase-installations").buildFileName = "firebase-installations.gradle"

include ":firebase-installations-interop"
project(":firebase-installations-interop").projectDir = new File(rootProject.projectDir, "local-libs/firebase-installations-interop")
project(":firebase-installations-interop").buildFileName = "firebase-installations-interop.gradle"