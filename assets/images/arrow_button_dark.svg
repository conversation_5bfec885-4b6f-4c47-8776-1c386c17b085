<svg width="110" height="110" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_399_273)">
<circle cx="50.5" cy="50.5" r="20.5" fill="#1F466F"/>
<path d="M47.0171 44.6753H55.6894V53.4228" stroke="#2687FD" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M44.2896 56.3951L55.6896 44.6753" stroke="#2687FD" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_399_273" x="0" y="0" width="101" height="101" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.184314 0 0 0 0 0.380392 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_399_273"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_399_273" result="shape"/>
</filter>
</defs>
</svg>
