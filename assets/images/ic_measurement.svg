<svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect width="39" height="39"/>
<!--<path d="M-8169 -660H24154V3577H-8169V-660Z"/>-->
<!--<path d="M-8169 -660V-661H-8170V-660H-8169ZM24154 -660H24155V-661H24154V-660ZM24154 3577V3578H24155V3577H24154ZM-8169 3577H-8170V3578H-8169V3577ZM-8169 -659H24154V-661H-8169V-659ZM24153 -660V3577H24155V-660H24153ZM24154 3576H-8169V3578H24154V3576ZM-8168 3577V-660H-8170V3577H-8168Z" fill="black" fill-opacity="0.28"/>-->
<g clip-path="url(#clip0_13133_27192)">
<!--<rect x="-374" y="-431" width="428" height="1809" rx="20"/>-->
<!--<g filter="url(#filter0_b_13133_27192)">-->
<!--<rect width="38.9573" height="38.8938" rx="8" fill="black" fill-opacity="0.53"/>-->
<!--</g>-->
<path d="M31 9.63333C31 9.8013 30.9287 9.96239 30.8019 10.0812C30.675 10.1999 30.5029 10.2667 30.3235 10.2667H8.67647C8.49706 10.2667 8.325 10.1999 8.19813 10.0812C8.07127 9.96239 8 9.8013 8 9.63333C8 9.46536 8.07127 9.30427 8.19813 9.1855C8.325 9.06673 8.49706 9 8.67647 9H30.3235C30.5029 9 30.675 9.06673 30.8019 9.1855C30.9287 9.30427 31 9.46536 31 9.63333ZM30.3235 26.7333H8.67647C8.49706 26.7333 8.325 26.8001 8.19813 26.9188C8.07127 27.0376 8 27.1987 8 27.3667C8 27.5346 8.07127 27.6957 8.19813 27.8145C8.325 27.9333 8.49706 28 8.67647 28H30.3235C30.5029 28 30.675 27.9333 30.8019 27.8145C30.9287 27.6957 31 27.5346 31 27.3667C31 27.1987 30.9287 27.0376 30.8019 26.9188C30.675 26.8001 30.5029 26.7333 30.3235 26.7333ZM17.2731 21.8516C17.146 21.7327 16.9738 21.6659 16.7941 21.6659C16.6145 21.6659 16.4422 21.7327 16.3152 21.8516C16.1882 21.9705 16.1168 22.1318 16.1168 22.3C16.1168 22.4682 16.1882 22.6295 16.3152 22.7484L19.0211 25.2817C19.0839 25.3407 19.1585 25.3875 19.2407 25.4194C19.3229 25.4514 19.411 25.4678 19.5 25.4678C19.589 25.4678 19.6771 25.4514 19.7593 25.4194C19.8415 25.3875 19.9161 25.3407 19.9789 25.2817L22.6848 22.7484C22.8118 22.6295 22.8832 22.4682 22.8832 22.3C22.8832 22.1318 22.8118 21.9705 22.6848 21.8516C22.5578 21.7327 22.3855 21.6659 22.2059 21.6659C22.0262 21.6659 21.854 21.7327 21.7269 21.8516L20.1765 23.3045V13.6955L21.7269 15.1484C21.854 15.2673 22.0262 15.3341 22.2059 15.3341C22.3855 15.3341 22.5578 15.2673 22.6848 15.1484C22.8118 15.0295 22.8832 14.8682 22.8832 14.7C22.8832 14.5318 22.8118 14.3705 22.6848 14.2516L19.9789 11.7183C19.9161 11.6593 19.8415 11.6125 19.7593 11.5806C19.6771 11.5486 19.589 11.5322 19.5 11.5322C19.411 11.5322 19.3229 11.5486 19.2407 11.5806C19.1585 11.6125 19.0839 11.6593 19.0211 11.7183L16.3152 14.2516C16.1882 14.3705 16.1168 14.5318 16.1168 14.7C16.1168 14.8682 16.1882 15.0295 16.3152 15.1484C16.4422 15.2673 16.6145 15.3341 16.7941 15.3341C16.9738 15.3341 17.146 15.2673 17.2731 15.1484L18.8235 13.6955V23.3045L17.2731 21.8516Z" fill="white" stroke="white" stroke-width="0.33"/>
</g>
<defs>
<filter id="filter0_b_13133_27192" x="-55" y="-55" width="148.957" height="148.894" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="27.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_13133_27192"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_13133_27192" result="shape"/>
</filter>
<clipPath id="clip0_13133_27192">
<rect x="-374" y="-431" width="428" height="1809" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
