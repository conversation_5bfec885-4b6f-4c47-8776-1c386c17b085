import 'package:scad_mobile/src/config/app_config/secret.dart';

/// Since CMS dependency was removed for Official Indicators, new domain IDs are created for official indicators.
/// Meanwhile, old domain IDs are still used for analytical app indicators.
const _newDomainIdMap = {
  'demo': {'1': 2234, '2': 4120, '3': 4119, '4': 2235, '5': 2237, '6': 2239},
  'dev': {'1': 575, '2': 3198, '3': 3199, '4': 581, '5': 579, '6': 574},
  'prod': {'1': 2234, '2': 4120, '3': 4119, '4': 2235, '5': 2237, '6': 2239},
  'stage': {'1': 2234, '2': 4120, '3': 4119, '4': 2235, '5': 2237, '6': 2239},
};

/// Get mapped domain ID by environment from [_newDomainIdMap].
String getMappedDomainId(String id) {
  Map<String, int> map;
  switch (Secret.appInstance.key) {
    case 'demo':
       map = _newDomainIdMap['demo']!;
    case 'dev':
      map =  _newDomainIdMap['dev']!;
    case 'prod':
      map =  _newDomainIdMap['prod']!;
    case 'stage':
      map =  _newDomainIdMap['stage']!;
    default:
      return id;
  }

  return map[id]?.toString() ?? id;
}
