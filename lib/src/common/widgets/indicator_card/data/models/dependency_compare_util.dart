part of 'indicator_details_response.dart';

/// Util class to help compare dependency in Indicator Filter Panel
class DependencyCompareUtil {
  DependencyCompareUtil(this.left, this.right, this.condition);

  final String left;
  final String right;
  final String condition;

  bool get _isDouble => double.tryParse(left) != null && double.tryParse(right) != null;

  bool get _isDate {
    final regex = RegExp(r'^\d{4}-?(\d{1,2})?-?(\d{1,2})?');
    return regex.hasMatch(left) && regex.hasMatch(right);
  }

  DateTime _parseDate(String text) {
    final arr = text.split('-');
    if (arr.length == 3) {
      return DateTime(
        int.parse(arr.first),
        int.parse(arr[1]),
        int.parse(arr.last),
      );
    } else if (arr.length == 2) {
      return DateTime(
        int.parse(arr.first),
        int.parse(arr.last),
      );
    } else {
      return DateTime(
        int.parse(arr.first),
      );
    }
  }

  bool get isValid {
    switch (condition) {
      case '==':
      case '===':
        if (_isDouble) return double.parse(left) == double.parse(right);
        if (_isDate) return _parseDate(left).compareTo(_parseDate(right)) == 0;
        return left == right;
      case '!=':
      case '!==':
        if (_isDouble) return double.parse(left) != double.parse(right);
        if (_isDate) return _parseDate(left).compareTo(_parseDate(right)) != 0;
        return left != right;
      case '>=':
        if (_isDouble) return double.parse(left) >= double.parse(right);
        if (_isDate) return _parseDate(left).compareTo(_parseDate(right)) >= 0;
        return false;
      case '<=':
        if (_isDouble) return double.parse(left) <= double.parse(right);
        if (_isDate) return _parseDate(left).compareTo(_parseDate(right)) <= 0;
        return false;
    }

    return false;
  }
}