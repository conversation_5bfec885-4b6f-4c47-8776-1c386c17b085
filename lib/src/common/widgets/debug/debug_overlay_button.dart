import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_persistent.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

part 'debug_bottom_panel.dart';

class DebugOverlayButton extends StatefulWidget {
  const DebugOverlayButton({super.key});

  @override
  State<DebugOverlayButton> createState() => _DebugOverlayButtonState();
}

class _DebugOverlayButtonState extends State<DebugOverlayButton> {
  Offset position = const Offset(0, 100);
  late final _screenSize = MediaQuery.of(context).size;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: GestureDetector(
        onPanUpdate: _handleDrag,
        onPanEnd: _handleDragEnd,
        child: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.7),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: IconButton(
            onPressed: () {
              OverlayEntry? entry;

              entry = OverlayEntry(
                builder: (context) =>  DebugBottomPanel(
                  onClose: () => entry?.remove(),
                ),
              );
              Overlay.of(context).insert(entry);
            },
            icon: const Icon(
              Icons.bug_report,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _handleDrag(DragUpdateDetails details) {
    setState(() {
      position = Offset(
        position.dx + details.delta.dx,
        position.dy + details.delta.dy,
      );

      // Keep the button within screen bounds
      position = Offset(
        position.dx.clamp(0, _screenSize.width - 50),
        position.dy.clamp(0, _screenSize.height - 50),
      );
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    // Snap to nearest edge
    double newX = position.dx;

    if (position.dx < _screenSize.width / 2) {
      // Snap to left
      newX = 10;
    } else {
      // Snap to right
      newX = _screenSize.width - 60;
    }

    setState(() {
      position = Offset(newX, position.dy);
    });
  }
}
