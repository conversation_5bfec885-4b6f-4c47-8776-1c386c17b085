import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/widgets/charts/chart_utils.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/utils/app_utils/string_utils.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/date_time_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class ColumnChart extends StatefulWidget {
  const ColumnChart({
    required this.chartDataList,
    required this.frequency,
    super.key,
    this.comparisonChartData = const [],
    this.dateRepresentation,
    this.isLightMode,
    this.isCompareActive = false,
    this.overrideAxisColorToDark = false,
    this.showMarker = false,
    this.yAxisLabel,
    this.yAxisLabelRight,
    this.isFullscreen = false,
  });

  final List<List<ColumnChartData>> chartDataList;
  final List<ColumnChartData> comparisonChartData;
  final String? dateRepresentation;
  final bool? isLightMode;
  final String frequency;
  final bool isCompareActive;
  final bool overrideAxisColorToDark;
  final bool showMarker;
  final String? yAxisLabel;
  final String? yAxisLabelRight;
  final bool isFullscreen;

  @override
  State<ColumnChart> createState() => _ColumnChartState();
}

class _ColumnChartState extends State<ColumnChart> {
  late bool isLightMode;

  late final _chartColors = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

  @override
  void initState() {
    super.initState();
    isLightMode = widget.isLightMode ?? HiveUtilsSettings.isLightMode;
  }

  bool get _hasMultipleColumns => widget.chartDataList.where((e) => e.isNotEmpty).length > 1;

  @override
  Widget build(BuildContext context) {
    final Map<String, DateTime> maxAndMin = setMaxAndMinValueColumnChart(widget.chartDataList);
    final dataLength = (widget.chartDataList.map((e) => e.length).toList()..sort()).lastOrNull ?? 0;

    final interval = ChartUtils.calculateInterval(
      dataLength: dataLength,
      frequency: widget.frequency,
      maxAndMin: maxAndMin,
    );

    final yAxisParams = _getYAxisParams();

    // print('_ColumnChartState.build: -----------key: ${widget.key}\n'
    //     '\t\t\t\tOG-MaxMin: ${maxAndMin['ogMin']} -> ${maxAndMin['ogMax']}\n'
    //     '\t\t\t\tmaxAndMin: ${maxAndMin['min']} -> ${maxAndMin['max']}\n'
    //     '\t\t\t\tinterval: $interval ; chart-type: ${widget.frequency} \n'
    //     '----------------------------------------');

    // Chart has only 1 point data
    final isSinglePointChart = dataLength == 1;

    final intervalType = isSinglePointChart
        ? DateTimeIntervalType.days
        : widget.frequency == 'Yearly'
            ? DateTimeIntervalType.years
            : DateTimeIntervalType.months;

    final dateFormat = widget.frequency == 'Monthly'
        ? DateFormat('MMM yyyy')
        : widget.frequency == 'Yearly'
            ? DateFormat('yyyy')
            : DateFormat('yyyy-M-dd');

    return ValueListenableBuilder(
      valueListenable: HiveUtilsSettings.textSizeFactorListenable,
      builder: (context, box, child) {
        final labelSize = 10.0 * min(HiveUtilsSettings.textSizeFactor, 1.1);

        return SfCartesianChart(
          trackballBehavior: _getChartTrackballBehavior(),
          zoomPanBehavior: ZoomPanBehavior(
            maximumZoomLevel: 1,
            enablePinching: true,
            zoomMode: ZoomMode.x,
            enablePanning: true,
            enableDoubleTapZooming: true,
            enableMouseWheelZooming: true,
          ),
          backgroundColor: isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
          plotAreaBorderWidth: 0,
          primaryXAxis: DateTimeAxis(
            labelRotation: -90,
            maximum: maxAndMin['max'],
            minimum: maxAndMin['min'],
            axisLine: AxisLine(
              width: 0.3,
              color: widget.overrideAxisColorToDark
                  ? (AppColors.greyF3F3F3)
                  : isLightMode
                      ? AppColors.black
                      : AppColors.white,
            ),
            majorTickLines: const MajorTickLines(width: 0),
            majorGridLines: MajorGridLines(
              width: 10,
              color: widget.overrideAxisColorToDark
                  ? (AppColors.greyF3F3F3)
                  : isLightMode
                      ? AppColors.greyShade15_1
                      : AppColors.blueShade33,
            ),
            interval: interval,
            plotOffset: 20,
            dateFormat: dateFormat,
            intervalType: intervalType,
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                widget.frequency == 'Quarterly'
                    ? IndicatorDateSetting.setupNameAll(
                        'Quarterly',
                        axisLabelRenderArgs.text,
                      )
                    : axisLabelRenderArgs.text,
                TextStyle(
                  color: widget.overrideAxisColorToDark
                      ? (AppColors.grey)
                      : isLightMode
                          ? AppColors.black
                          : AppColors.white,
                  fontSize: labelSize,
                ),
              );
            },
          ),
          primaryYAxis: NumericAxis(
            title: AxisTitle(
              text: widget.yAxisLabel,
              textStyle: TextStyle(
                fontSize: labelSize,
                color: isLightMode ? AppColors.black : AppColors.white,
              ),
            ),
            name: 'yAxis1',
            numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
            rangePadding: ChartRangePadding.round,
            minimum: yAxisParams.minimum,
            maximum: yAxisParams.maximum,
            interval: yAxisParams.interval,
            axisLine: AxisLine(
              width: 0.3,
              color: widget.overrideAxisColorToDark
                  ? (AppColors.greyF3F3F3)
                  : isLightMode
                      ? AppColors.black
                      : AppColors.white,
            ),
            axisLabelFormatter: (axisLabelRenderArgs) {
              return ChartAxisLabel(
                axisLabelRenderArgs.text,
                TextStyle(
                  fontSize: labelSize,
                  color: widget.comparisonChartData.isNotEmpty
                      ? _chartColors.first
                      : widget.overrideAxisColorToDark
                          ? (AppColors.grey)
                          : isLightMode
                              ? AppColors.black
                              : AppColors.white,
                ),
              );
            },
            majorTickLines: const MajorTickLines(width: 0),
            minorTickLines: const MinorTickLines(width: 0),
            majorGridLines: const MajorGridLines(width: 0),
            minorGridLines: const MinorGridLines(width: 0),
          ),
          axes: [
            if (widget.isCompareActive)
              NumericAxis(
                name: 'yAxis2',
                opposedPosition: true,
                numberFormat: NumberFormat.compactSimpleCurrency(name: ''),
                rangePadding: ChartRangePadding.round,
                title: AxisTitle(
                  text: widget.yAxisLabelRight,
                  textStyle: TextStyle(
                    fontSize: labelSize,
                    color: isLightMode ? AppColors.black : AppColors.white,
                  ),
                ),
                axisLine: const AxisLine(color: AppColors.greyF3F4F6),
                axisLabelFormatter: (axisLabelRenderArgs) {
                  return ChartAxisLabel(
                    axisLabelRenderArgs.text,
                    TextStyle(
                      fontSize: 10 * HiveUtilsSettings.textSizeFactor,
                      color: AppColors.chartColorSet[1],
                    ),
                  );
                },
                majorTickLines: const MajorTickLines(width: 0),
                minorTickLines: const MinorTickLines(width: 0),
                majorGridLines: const MajorGridLines(width: 0),
                minorGridLines: const MinorGridLines(width: 0),
              ),
          ],
          series: _series,
        );
      },
    );
  }

  List<CartesianSeries<ColumnChartData, DateTime>> get _series {
    final seriesList = <CartesianSeries<ColumnChartData, DateTime>>[];

    final chartDataList = widget.chartDataList.where((e) => e.isNotEmpty);
    for (int i = 0; i < chartDataList.length; i++) {
      final chartData = chartDataList.elementAt(i);
      final series = ColumnSeries<ColumnChartData, DateTime>(
        dataLabelSettings: _getDataLabelSettings(i),
        animationDuration: 300,
        name: 'xAxis',
        width: chartData.length <= 1 ? 1 : 0.3,
        spacing: 0.2,
        pointColorMapper: (ColumnChartData data, _) => null,
        dataSource: chartData,
        xValueMapper: (ColumnChartData data, _) {
          return IndicatorDateSetting.dateFormatterForChart(
            widget.frequency,
            data.x,
          );
        },
        yValueMapper: (ColumnChartData data, _) => data.y,
        color: _chartColors[widget.chartDataList.indexOf(chartData) % _chartColors.length],
        yAxisName: widget.isCompareActive
            ? widget.chartDataList.indexOf(chartData) == 0
                ? 'yAxis1'
                : 'yAxis2'
            : 'yAxis1',
      );
      seriesList.add(series);
    }

    // Add comparison series if available
    if (widget.comparisonChartData.isNotEmpty) {
      final compareSeries = ColumnSeries<ColumnChartData, DateTime>(
        dataLabelSettings: !widget.showMarker
            ? const DataLabelSettings()
            : const DataLabelSettings(
                isVisible: true,
                labelPosition: ChartDataLabelPosition.outside,
              ),
        animationDuration: 300,
        width: 0.3,
        pointColorMapper: (ColumnChartData data, _) => null,
        dataSource: widget.comparisonChartData,
        xValueMapper: (ColumnChartData data, _) => IndicatorDateSetting.dateFormatterForChart(
          widget.frequency,
          data.x,
        ),
        yValueMapper: (ColumnChartData data, _) => data.y,
        color: AppColors.compareChartLine,
        yAxisName: 'yAxis2',
      );
      seriesList.add(compareSeries);
    }

    return seriesList;
  }

  DataLabelSettings _getDataLabelSettings(int index) {
    if (!widget.showMarker) {
      return const DataLabelSettings();
    }

    final color = _chartColors[index & _chartColors.length];

    return DataLabelSettings(
      isVisible: true,
      useSeriesColor: true,
      margin: EdgeInsets.zero,
      color: color,
      borderColor: color,
      labelPosition: ChartDataLabelPosition.outside,
      overflowMode: OverflowMode.shift,
      angle: 90,
      builder: (data, point, series, pointIndex, seriesIndex) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            ShortNumberPipe.transform(
                  number: point.y.toString(),
                  angularPipeFormat: '1.0-2',
                ) ??
                '',
            style: const TextStyle(
              fontSize: 10,
              color: AppColors.white,
            ),
          ),
        );
      },
    );
  }

  TrackballBehavior _getChartTrackballBehavior() {
    final trackballDisplayMode =
        widget.chartDataList.length > 12 ? TrackballDisplayMode.nearestPoint : TrackballDisplayMode.groupAllPoints;

    return TrackballBehavior(
      enable: true,
      activationMode: ActivationMode.singleTap,
      tooltipDisplayMode: trackballDisplayMode,
      shouldAlwaysShow: true,
      tooltipAlignment: ChartAlignment.far,
      lineDashArray: const [4, 2],
      markerSettings: const TrackballMarkerSettings(
        height: 5,
        width: 5,
        borderColor: AppColors.greyShade2,
        borderWidth: 0.5,
        markerVisibility: TrackballVisibilityMode.hidden,
      ),
      tooltipSettings: const InteractiveTooltip(
        format: 'point.y',
        color: AppColors.greyShade3,
        textStyle: TextStyle(
          color: AppColors.greyShade19,
        ),
      ),
    );
  }

  Map<String, DateTime> setMaxAndMinValueColumnChart(List<List<ColumnChartData>> chartDataList) {
    final now = DateTime.now().abs();
    final frequency = widget.frequency;

    try {
      final x = chartDataList.where((e) => e.isNotEmpty).firstOrNull?.firstOrNull?.x;
      final initial = x == null ? now : DateTime.tryParse(x) ?? now;
      DateTime ogMax = initial;
      DateTime ogMin = initial;

      for (final collection in chartDataList) {
        for (final element in collection) {
          final date = DateTime.tryParse(element.x)?.abs() ?? now;
          if (date.compareTo(ogMax) > 0) ogMax = date;
          if (date.compareTo(ogMin) < 0) ogMin = date;
        }
      }

      DateTime max = ogMax.copyWith();
      DateTime min = ogMin.copyWith();

      final diff = max.difference(min);

      if (diff.inDays == 0) {
        max = max.add(const Duration(days: 1));
        min = min.subtract(const Duration(days: 1));
      }

      final dataLength = (chartDataList.map((e) => e.length).toList()..sort()).lastOrNull ?? 0;

      // Fix for https://scad.atlassian.net/browse/BM-136
      // If data length is less than 3, the bar gets cropped as the screen size increases
      if (_hasMultipleColumns || dataLength < 3) {
        if (frequency == 'Monthly') {
          min = min.beginningOfPreviousMonth;
          max = max.beginningOfNextMonth;
        }
        if (frequency == 'Yearly') {
          min = min.previousYear;
          max = max.nextYear;
        }
      }

      return {'max': max, 'min': min, 'ogMax': ogMax, 'ogMin': ogMin};
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return {'max': now, 'min': now};
    }
  }

  AxisParams<double> _getYAxisParams() {
    final seriesList = List<List<ColumnChartData>>.from(widget.chartDataList);

    double? minValue = seriesList.firstOrNull?.firstOrNull?.y ?? 0.0;
    double? maxValue = seriesList.lastOrNull?.lastOrNull?.y ?? 0.0;
    // Check all chart data lists
    for (final series in seriesList) {
      for (final point in series) {
        final y = point.y ?? 0.0;
        if (minValue == null || y < minValue) minValue = y;
        if (maxValue == null || y > maxValue) maxValue = y;
      }
    }

    minValue = minValue?.floor().toDouble();
    maxValue = maxValue?.ceil().toDouble();

    // If range is too small, force a reasonable interval
    if (minValue != null && maxValue != null) {
      final dataLength = seriesList.fold(0, (value, seriesData) => value += seriesData.length);
      if (dataLength == 1) {
        final power = maxValue.toString().split('.').first.length;
        final offset = pow(10, power) / 4;
        maxValue += offset;
        minValue -= offset;
      }

      final range = maxValue - minValue;
      if (range <= 10) {
        final interval = range <= 5 ? 1.0 : 2.0;
        return AxisParams(
          minimum: minValue,
          maximum: maxValue,
          interval: interval <= 0 ? null : interval,
        );
      }
    }

    return const AxisParams();
  }
}

class ColumnChartData {
  ColumnChartData(this.x, this.y, this.y1, this.y2);

  final String x;
  final double? y;
  final double? y1;
  final double? y2;
}
