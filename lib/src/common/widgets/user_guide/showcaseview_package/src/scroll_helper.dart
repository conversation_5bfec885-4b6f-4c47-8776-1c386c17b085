import 'package:flutter/material.dart';

extension ScrollHelper on ScrollController {
  Future<void> scrollToWidget(BuildContext context, GlobalKey key, VoidCallback onDone) async {
    if (key.currentContext != null) {
      jumpTo(0);

      if (!context.mounted) return;

      final RenderBox renderBox = key.currentContext!.findRenderObject()! as RenderBox;

      final double scrollOffset = renderBox.localToGlobal(Offset.zero).dy;
      final double screenPaddingTop = MediaQuery.of(context).padding.top;

      final double screenHeight = MediaQuery.of(context).size.height;
      final double widgetHeight = renderBox.hasSize ? renderBox.size.height : 0;

      final double targetScrollOffset = scrollOffset - screenHeight + widgetHeight + screenPaddingTop;

      await animateTo(
        targetScrollOffset > 0 ? targetScrollOffset : 0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.linear,
      );

      onDone();
    }
  }
}
