import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class SlimAppBar extends StatelessWidget implements PreferredSizeWidget {
  const SlimAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.trailing,
    this.centerTitle = true,
    this.onBack,
    this.useMaterialNavigator = false,
    this.hideBackButton = false,
  });

  final String? title;
  final Widget? titleWidget;
  final bool centerTitle;
  final bool useMaterialNavigator;
  final Widget? trailing;
  final VoidCallback? onBack;

  final bool hideBackButton;

  static const double _size = 56;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return AppBar(
      toolbarHeight: _size,
      backgroundColor: AppColors.scaffoldBackground,
      leadingWidth: hideBackButton ? 0 : null,
      surfaceTintColor: Colors.transparent,
      leading: hideBackButton
          ? const SizedBox.shrink()
          : Material(
              color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onBack ??
              () {
                if (useMaterialNavigator) {
                  Navigator.of(context).maybePop();
                } else {
                  context.back();
                }
              },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 14,
              vertical: 6,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                RotatedBox(
                  quarterTurns: DeviceType.isDirectionRTL(
                    context,
                  )
                      ? 2
                      : 0,
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: SvgPicture.asset(
                      AppImages.icArrowLeft,
                      colorFilter: ColorFilter.mode(
                        AppColors.blue,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      centerTitle: centerTitle,
      title:  titleWidget ?? (title == null
          ? null
          : Text(
              title!,
              style: TextStyle(
                color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            )),
      actions: [
        trailing ?? const SizedBox(),
      ],
    );
  }

  @override
  Size get preferredSize => const Size(double.infinity, _size);
}
