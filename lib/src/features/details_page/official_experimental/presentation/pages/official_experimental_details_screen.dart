import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/chart_legend.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/icon_and_title_widget.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_value_v2.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/enum.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/scroll_helper.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/base/constants.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/indicator_details_bloc_mixin.dart';
import 'package:scad_mobile/src/features/details_page/official_experimental/presentation/bloc/official_experimental_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_compute_operation_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_data_frequency_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_data_presentation_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_action_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_view/chart_view.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_view/no_chart_data.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chat_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/domain_and_personalize_controls/domain_and_personalize_controls.dart';
import 'package:scad_mobile/src/features/details_page/widgets/download_as/download_as.dart';
import 'package:scad_mobile/src/features/details_page/widgets/full_screen_chart_view/full_screen_chart_view.dart';
import 'package:scad_mobile/src/features/details_page/widgets/glossary_container.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_chart_header.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_filter/indicator_filter_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_type_tag.dart';
import 'package:scad_mobile/src/features/details_page/widgets/meta_data_widget.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/date_time_extensions.dart';
import 'package:scad_mobile/src/utils/extentions/list_extensions.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';

@RoutePage()
class OfficialExperimentalDetailsScreen extends StatefulWidget {
  const OfficialExperimentalDetailsScreen({
    required this.id,
    required this.contentType,
    required this.title,
    this.screenerPayload,
    super.key,
  });

  final String id;
  final String contentType;
  final String title;
  final JSONObject? screenerPayload;

  @override
  State<OfficialExperimentalDetailsScreen> createState() => _OfficialExperimentalDetailsScreenState();
}

class _OfficialExperimentalDetailsScreenState extends State<OfficialExperimentalDetailsScreen> {
  final isLightMode = HiveUtilsSettings.isLightMode;
  final _isLightMode = HiveUtilsSettings.isLightMode;

  IndicatorDetailsResponse? get _indicatorDetails => context.read<OfficialExperimentalDetailsBloc>().indicatorDetails;

  VisualizationsMeta? get _selectedVisualizationMeta =>
      context.read<OfficialExperimentalDetailsBloc>().selectedVisualizationMeta;

  String? get yAxisLabel => _selectedVisualizationMeta?.yAxisLabel;

  List<List<Map<String, dynamic>>> get seriesList =>
      _isRecentOn ? _seriesList.map((e) => e.reversed.take(12).toList().reversed.toList()).toList() : _seriesList;

  final ScrollController _scrollController = ScrollController();
  final ScreenshotController _screenshotController = ScreenshotController();

  final GlobalKey chatButtonKey = GlobalKey(debugLabel: 'chatButtonKey');
  final GlobalKey changeFrequencyButtonKey = GlobalKey(debugLabel: 'changeFrequencyButtonKey');
  final GlobalKey changePresentationButtonKey = GlobalKey(debugLabel: 'changePresentationButtonKey');
  final GlobalKey compareIndicatorsButtonKey = GlobalKey(debugLabel: 'compareIndicatorsButtonKey');
  final GlobalKey computeDataButtonKey = GlobalKey(debugLabel: 'computeDataButtonKey');
  final GlobalKey downloadAsButtonKey = GlobalKey(debugLabel: 'downloadAsButtonKey');
  List<GlobalKey> steps = [];
  BuildContext? myContext;
  bool disableAppbarSlide = HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home;

  List<List<Map<String, dynamic>>> _seriesList = [];

  ChartDataRepresentation _selectedChartRepresentation = ChartDataRepresentation.line;

  final _chartRepresentationPreviewNotifier = ValueNotifier<ChartDataRepresentation?>(null);

  bool get _isRecentOn =>
      _periodOptionList.value.where((e) => e.isSelected).firstOrNull?.label == LocaleKeys.recent.tr();

  final ValueNotifier<List<Options>> _periodOptionList = ValueNotifier([]);
  final ValueNotifier<bool> _isFullScreenChartViewEnabled = ValueNotifier(true);

  @override
  void initState() {
    super.initState();

    context.read<OfficialExperimentalDetailsBloc>().resetBlocVariables();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      steps = [
        chatButtonKey,
        changeFrequencyButtonKey,
        changePresentationButtonKey,
        compareIndicatorsButtonKey,
        computeDataButtonKey,
        downloadAsButtonKey,
      ];
      _loadIndicatorDetails();
    });
  }

  void _loadIndicatorDetails() {
    context.read<OfficialExperimentalDetailsBloc>().add(
          GetIndicatorDetailsEvent(
            id: widget.id,
            contentType: widget.contentType,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: ShowCaseWidget(
          builder: Builder(
            builder: (context) {
              myContext = context;
              disableAppbarSlide = HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home;
              return Stack(
                children: [
                  Column(
                    children: [
                      FlatAppBar(
                        title: widget.title,
                        bottomPadding: 0,
                        isDetailsPage: disableAppbarSlide ? false : true,
                        scrollController: disableAppbarSlide ? null : _scrollController,
                        indicatorType: _indicatorDetails?.getIndicatorType(),
                      ),
                      Expanded(
                        child: BlocConsumer<OfficialExperimentalDetailsBloc, DetailsBaseState>(
                          listenWhen: (_, state) => state is GetIndicatorDetailsSuccessState,
                          buildWhen: (_, state) => state is GetIndicatorDetailsBaseState,
                          listener: (context, state) async {
                            if (state is GetIndicatorDetailsSuccessState) {
                              context.read<OfficialExperimentalDetailsBloc>().initializeDefaultFilter();
                              _setSeriesList();
                              _setPeriodFilter();

                              WidgetsBinding.instance.addPostFrameCallback((_) async {
                                if (mounted) {
                                  if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
                                    ShowCaseWidget.of(myContext!).startShowCase(steps);
                                    await _scrollController.scrollToWidget(
                                        context, chatButtonKey, () => setState(() {}));
                                  }
                                }
                              });
                            }
                          },
                          builder: (context, state) {
                            if (state is GetIndicatorDetailsErrorState) {
                              return Center(
                                child: ErrorReloadPlaceholder(error: state.error, onReload: _loadIndicatorDetails),
                              );
                            }

                            if (state is GetIndicatorDetailsLoadingState || _indicatorDetails == null) {
                              return const Center(child: CircularProgressIndicator());
                            }

                            return Column(
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: _bodyContent(state),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  if (disableAppbarSlide && ShowCaseWidget.of(myContext!).activeWidgetId == null)
                    Positioned.fill(child: Container(color: Colors.black45)),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _bodyContent(DetailsBaseState state) {
    return SingleChildScrollView(
      controller: _scrollController,
      child: Container(
        constraints: BoxConstraints(minHeight: MediaQuery.sizeOf(context).height),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: DomainAndPersonalizeControls(
                indicatorDetails: _indicatorDetails!,
                contentType: widget.contentType,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 18),
              decoration: BoxDecoration(
                color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 10),
                  _buildChartHeader(),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildIndicatorValues(),
                        const SizedBox(height: 15),
                        Screenshot(
                          key: Key('official.experimental.details.chart.${widget.id}'),
                          controller: _screenshotController,
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(minHeight: 200),
                            child: _buildChartAndLegends(),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildPeriodFilter(),
                            _buildChatButton(),
                          ],
                        ),
                        const SizedBox(height: 20),
                        _buildActionButtons(),
                        _buildDownloadAs(),
                        const SizedBox(height: 30),
                        _buildMetaData(),
                        _buildUpdatedOnAndSource(),
                      ],
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: GlossaryContainer(),
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartHeader() {
    final indicatorType = _indicatorDetails?.getIndicatorType();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: IndicatorChartHeader(
        filterBuilder: (context) {
          final bloc = context.read<OfficialExperimentalDetailsBloc>();
          final filterPanel = bloc.filterPanel;

          if (filterPanel is! FilterPanel) {
            return const SizedBox.shrink();
          }

          return IndicatorFilterButton(
            adapter: bloc,
            onFilterApplied: (filterMap) {
              context.read<OfficialExperimentalDetailsBloc>().add(
                    ApplyOfficialExperimentalFilterEvent(payload: filterMap),
                  );
            },
          );
        },
        security: _indicatorDetails?.security,
        tag: indicatorType == IndicatorType.official
            ? const IndicatorTypeTag.official()
            : const IndicatorTypeTag.experimental(),
        fullScreenButtonBuilder: (context) => ValueListenableBuilder(
          valueListenable: _isFullScreenChartViewEnabled,
          builder: (context, isEnabled, child) {
            return Opacity(
              opacity: isEnabled ? 1 : 0.5,
              child: FullScreenExpandButton(
                onTap: isEnabled
                    ? () => FullScreenChartView.show(
                          context: context,
                          title: widget.title,
                          chartBuilder: (context) => _buildChart(isFullScreen: true),
                          legendBuilder: (context) => _buildLegends(),
                        )
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }

  OverView? _overview;

  Widget _buildIndicatorValues() {
    return BlocBuilder<OfficialExperimentalDetailsBloc, DetailsBaseState>(
      buildWhen: (_, state) => state is GetIndicatorOverviewSuccessState || state is GetIndicatorDetailsSuccessState,
      builder: (context, state) {
        if (state is GetIndicatorDetailsSuccessState) {
          context.read<OfficialExperimentalDetailsBloc>().add(
                GetIndicatorOverviewEvent(
                  ids: [widget.id],
                  type: widget.contentType,
                ),
              );
        }

        if (state is GetIndicatorOverviewSuccessState && state.overview.value != null) {
          if (!state.event.ids.contains(widget.id)) {
            _overview = null;
          } else {
            _overview = state.overview;
          }
        }

        return AnimatedSize(
          curve: Curves.fastLinearToSlowEaseIn,
          duration: const Duration(milliseconds: 400),
          child: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: IndicatorValueV2(
              key: UniqueKey(),
              isCardView: true,
              indicatorDetails: _indicatorDetails,
              overView: _overview,
            ),
          ),
        );
      },
    );
  }

  void _setPeriodFilter() {
    final showRecent = _seriesList.any((e) => e.length > 12);
    _periodOptionList.value = [
      ...(_indicatorDetails?.indicatorFilters?.firstOrNull?.options ?? []),
      if (showRecent) Options(id: LocaleKeys.recent.tr(), label: LocaleKeys.recent.tr()),
    ];
    _periodOptionList.value.lastOrNull?.isSelected = true;
  }

  Widget _buildUpdatedOnAndSource() {
    if (DeviceType.isTab()) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _dataUpdatedOn(),
          _dataSource(),
        ],
      );
    } else {
      return Align(
        alignment: DeviceType.isDirectionRTL(context) ? Alignment.centerRight : Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _dataUpdatedOn(),
            const SizedBox(height: 17),
            _dataSource(),
            const SizedBox(height: 6),
          ],
        ),
      );
    }
  }

  Widget _buildActionButtons() {
    return Builder(
      builder: (context) {
        context.watch<OfficialExperimentalDetailsBloc>();
        final width = MediaQuery.sizeOf(context).width;
        final hasSeries = _seriesList.any((e) => e.isNotEmpty);
        final timeUnits = _indicatorDetails?.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.timeUnit ?? [];
        final hasFilterPanel = _indicatorDetails?.filterPanel is FilterPanel;

        final isComparable = _indicatorDetails?.getIndicatorType() == IndicatorType.official &&
            _indicatorDetails?.isMultiDimension == false &&
            _indicatorDetails?.theme != null &&
            _indicatorDetails?.subtheme != null;

        return Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: IntroWidget(
                    stepKey: changeFrequencyButtonKey,
                    stepIndex: 6,
                    totalSteps: 10,
                    title: LocaleKeys.changeDataFrequency.tr(),
                    description: LocaleKeys.changeDataFrequencyGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomLeft,
                    position: TooltipPosition.top,
                    targetBorderRadius: 6,
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.12, left: width * 0.12),
                    onPrevious: () {
                      _scrollController.scrollToWidget(context, chatButtonKey, () => setState(() {}));
                    },
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.frequency(
                      enabled: false,
                      onPressed: () => SelectDataFrequencyBottomSheet.show(
                        context,
                        onPreview: (frequency) {
                          // TODO(Jerin): Ask Hafjash
                        },
                        onDone: (frequency) {
                          // TODO(Jerin): Ask Hafjash
                        },
                        timeUnits: timeUnits,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: IntroWidget(
                    stepKey: changePresentationButtonKey,
                    stepIndex: 7,
                    totalSteps: 10,
                    title: LocaleKeys.changeDataPresentation.tr(),
                    description: LocaleKeys.changeDataPresentationGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomLeft,
                    position: TooltipPosition.top,
                    targetBorderRadius: 6,
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.34, left: width * 0.34),
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.presentation(
                      enabled: hasSeries,
                      onPressed: () async {
                        _chartRepresentationPreviewNotifier.value = _selectedChartRepresentation;

                        await SelectDataPresentationBottomSheet.show(
                          context,
                          options: [
                            ChartDataRepresentation.line,
                            ChartDataRepresentation.bar,
                            ChartDataRepresentation.table,
                          ],
                          initialRepresentation: _selectedChartRepresentation,
                          onPreview: (value) => _chartRepresentationPreviewNotifier.value = value,
                          onDone: (value) => _selectedChartRepresentation = value,
                        );
                        _chartRepresentationPreviewNotifier.value = null;
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: IntroWidget(
                    stepKey: compareIndicatorsButtonKey,
                    stepIndex: 8,
                    totalSteps: 10,
                    title: LocaleKeys.compareIndicators.tr(),
                    description: LocaleKeys.compareDataGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomRight,
                    position: TooltipPosition.top,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    targetBorderRadius: 6,
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.33, left: width * 0.33),
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.compare(
                      enabled: isComparable && hasSeries,
                      onPressed: () => context.pushRoute(
                        SelectComparableScreenRoute(
                          indicatorDetails: _indicatorDetails!,
                          screener: widget.screenerPayload,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: IntroWidget(
                    stepKey: computeDataButtonKey,
                    stepIndex: 9,
                    totalSteps: 10,
                    title: LocaleKeys.computeData.tr(),
                    description: LocaleKeys.computeDataGuideDesc.tr(),
                    arrowAlignment: Alignment.bottomRight,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    position: TooltipPosition.top,
                    targetBorderRadius: 6,
                    onNext: () async {
                      await _scrollController.scrollToWidget(context, downloadAsButtonKey, () => setState(() {}));
                    },
                    isDownArrow: true,
                    arrowPadding: EdgeInsets.only(top: 10, right: width * 0.1, left: width * 0.1),
                    targetPadding: const EdgeInsets.symmetric(vertical: 10),
                    child: ChartActionButton.compute(
                      enabled: hasFilterPanel && hasSeries,
                      onPressed: () => SelectComputeOperationBottomSheet.show(
                        context,
                        onOperationSelected: (operation) {
                          final filterPanel = _indicatorDetails?.filterPanel as FilterPanel?;
                          final propertiesList = List<Properties>.from(filterPanel?.properties ?? []);

                          context.pushRoute(
                            SelectComputableScreenRoute(
                              indicatorId: _indicatorDetails?.indicatorId ?? '',
                              operation: operation,
                              propertiesList: propertiesList,
                              security: _indicatorDetails?.security,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        );
      },
    );
  }

  Widget _buildDownloadAs() {
    return ValueListenableBuilder(
      valueListenable: _periodOptionList,
      builder: (context, value, child) {
        final seriesListForDownloadAs = _filterPeriod(_seriesList);

        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, value, child) {
            final isRestricted = _indicatorDetails?.security?.id != '0';
            final ChartDataFrequency frequency =
                IndicatorDateSetting.getChartDataFrequency(seriesList: seriesList, indicatorDetails: _indicatorDetails);

            return IntroWidget(
              stepKey: downloadAsButtonKey,
              stepIndex: 10,
              totalSteps: 10,
              title: LocaleKeys.downloadAs.tr(),
              description: LocaleKeys.downloadAsGuideDesc.tr(),
              arrowAlignment: Alignment.bottomLeft,
              position: TooltipPosition.top,
              crossAxisAlignment: CrossAxisAlignment.center,
              targetBorderRadius: 10,
              isDownArrow: true,
              arrowPadding: const EdgeInsets.only(top: 10),
              targetPadding: const EdgeInsets.all(10),
              onPrevious: () {
                _scrollController.scrollToWidget(context, changePresentationButtonKey, () => setState(() {}));
              },
              child: DownloadAsV2(
                key: Key('${_selectedChartRepresentation.index}'),
                title: _indicatorDetails?.componentTitle ?? '',
                description: _indicatorDetails?.componentSubtitle ?? '',
                seriesList: seriesListForDownloadAs,
                isRestricted: isRestricted,
                isTableView: _selectedChartRepresentation == ChartDataRepresentation.table,
                chart: _buildChartAndLegends(
                  enforceLightMode: true,
                  showTooltips: true,
                ),
                tableFieldList: _indicatorDetails?.tableFields,
                frequency: frequency,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildChartAndLegends({
    bool? enforceLightMode,
    bool showTooltips = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildChart(
          enforceLightMode: enforceLightMode,
          showTooltips: showTooltips,
        ),
        _buildLegends(enforceLightMode: enforceLightMode),
      ],
    );
  }

  Widget _buildChart({
    bool? enforceLightMode,
    bool isFullScreen = false,
    bool showTooltips = false,
  }) {
    return ValueListenableBuilder(
      valueListenable: _periodOptionList,
      builder: (context, _, __) {
        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, _, __) {
            final chartRepresentation = _chartRepresentationPreviewNotifier.value ?? _selectedChartRepresentation;
            _setSeriesList();

            final list = _filterPeriod(_seriesList);
            final hasData = list.any((e) => e.isNotEmpty);
            if (!hasData) {
              SchedulerBinding.instance.addPostFrameCallback(
                (_) => _isFullScreenChartViewEnabled.value = false,
              );
              return const NoChartData();
            }

            final String freqString = IndicatorDateSetting.setFrequancy(
              l: list,
              indicatorDetails: IndicatorDetailsResponseHelper(_indicatorDetails!),
            )['selectedFrequencyForFilter'] as String;

            SchedulerBinding.instance.addPostFrameCallback(
              (_) => _isFullScreenChartViewEnabled.value = true,
            );
            final frequency = ChartDataFrequency.fromString(freqString);
            return ChartView(
              chartSeriesData: ChartSeriesData(
                series: list,
                tableFields: _indicatorDetails?.tableFields,
              ),
              frequency: frequency,
              chartRepresentation: chartRepresentation,
              enforceLightMode: enforceLightMode,
              showTooltips: showTooltips,
              isFullScreenView: isFullScreen,
              yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
            );
          },
        );
      },
    );
  }

  Widget _buildLegends({bool? enforceLightMode}) {
    final isLightMode = enforceLightMode ?? this.isLightMode;
    final selectedFilter = context.watch<OfficialExperimentalDetailsBloc>().dependencyResolvedFilters;
    final colorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    return ValueListenableBuilder(
      valueListenable: _chartRepresentationPreviewNotifier,
      builder: (context, previewChartType, child) {
        final selectedChartRepresentation = previewChartType ?? _selectedChartRepresentation;

        if (selectedChartRepresentation == ChartDataRepresentation.table) {
          return const SizedBox.shrink();
        }

        if (selectedFilter.isEmpty) {
          return ChartLegend(
            label: _indicatorDetails?.componentTitle ?? '',
            color: colorSet.first,
            isLightMode: isLightMode,
          );
        }

        final multiFilter = selectedFilter.entries
            .firstWhere(
              (entry) => entry.key != kObsDt && entry.value.length > 1,
              orElse: () => selectedFilter.entries.firstWhere((entry) => entry.key != kObsDt),
            )
            .value;

        return Wrap(
          spacing: 12,
          runSpacing: 8,
          children: multiFilter.indexed
              .map(
                (e) => ChartLegend(
                  label: e.$2,
                  color: colorSet[e.$1 % colorSet.length],
                  isLightMode: isLightMode,
                ),
              )
              .toList(),
        );
      },
    );
  }

  Widget _buildMetaData() {
    final metadata = _indicatorDetails?.metaData ?? [];

    if (metadata.isEmpty) {
      return const SizedBox();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: MetaDataWidget(
        id: widget.id,
        title: widget.title,
        metaData: _indicatorDetails?.metaData,
      ),
    );
  }

  Widget _dataUpdatedOn() {
    final IndicatorCategory category = _indicatorDetails?.getIndicatorCategory() ?? IndicatorCategory.other;

    if (category == IndicatorCategory.official) {
      return IconAndTitleWidget(
        icon: AppImages.icCalendar,
        title: LocaleKeys.updatedOn.tr(),
        content: DateTime.tryParse(
              '${_indicatorDetails?.updated}',
            )?.toFormattedDateTimeString('dd/MM/yyyy') ??
            _indicatorDetails?.updated ??
            '_',
      );
    } else if (category == IndicatorCategory.experimental || category == IndicatorCategory.analyticalApps) {
      return IconAndTitleWidget(
        icon: AppImages.icCalendar,
        title: LocaleKeys.updatedOn.tr(),
        content: DateTime.tryParse(
              '${_indicatorDetails?.publicationDate}',
            )?.toFormattedDateTimeString('dd/MM/yyyy') ??
            _indicatorDetails?.publicationDate ??
            '_',
      );
    } else {
      return const SizedBox();
    }
  }

  Widget _dataSource() {
    if (_indicatorDetails?.dataSource != null && _indicatorDetails?.dataSource?.trim() != '') {
      return IconAndTitleWidget(
        icon: AppImages.icDocument,
        title: LocaleKeys.source.tr(),
        content: _indicatorDetails?.dataSource ?? '-',
      );
    } else {
      return const SizedBox();
    }
  }

  void _setSeriesList() {
    final seriesDataList = _selectedVisualizationMeta?.seriesMeta!.map((e) => e.data ?? []).toList() ?? [];
    final selectedFilter = context.read<OfficialExperimentalDetailsBloc>().dependencyResolvedFilters;
    if (selectedFilter.isEmpty) {
      _seriesList = seriesDataList;
      return;
    }

    _seriesList.clear();
    final multiFilter = selectedFilter.entries.firstWhere(
      (entry) => entry.key != kObsDt && entry.value.length > 1,
      orElse: () => selectedFilter.entries.firstWhere((entry) => entry.key != kObsDt),
    );

    final otherFilters = selectedFilter.entries.where((e) => e.key != multiFilter.key);

    bool isOtherFiltersSatisfied(JSONObject data) {
      if (otherFilters.isEmpty) return true;

      return otherFilters.every(
        (entry) => entry.value.any(
          (e) => e.toLowerCase() == data[entry.key]?.toLowerCase(),
        ),
      );
    }

    final key = multiFilter.key;
    for (final option in multiFilter.value) {
      final series = <JSONObject>[];
      for (final data in seriesDataList.first) {
        if (data[key].toString().toLowerCase() != option.toLowerCase() || !isOtherFiltersSatisfied(data)) continue;
        series.add(data);
      }

      _seriesList.add(series);
    }
  }

  SeriesDataList _filterPeriod(SeriesDataList list) {
    SeriesDataList filteredData = [];

    try {
      final Options period =
          _periodOptionList.value.firstWhere((e) => e.isSelected, orElse: () => _periodOptionList.value.last);

      if (period.label?.toLowerCase() == 'all') {
        filteredData = list;
      } else if (period.label?.toLowerCase() == LocaleKeys.recent.tr().toLowerCase()) {
        filteredData = list.map((e) => e.limitListLength()).toList();
      } else {
        final DateTime startDate =
            (_selectedVisualizationMeta?.seriesMeta?.firstOrNull?.xMax ?? '').toDateTime() ?? DateTime.now();

        for (final List<Map<String, dynamic>> element in list) {
          late DateTime endDate;
          if (period.unit?.toLowerCase() == 'years') {
            endDate = DateTime(startDate.year - (period.value ?? 0), startDate.month, startDate.day);
          } else {
            endDate = DateTime(startDate.year, startDate.month - (period.value ?? 0), startDate.day);
          }

          final List<Map<String, dynamic>> tempList = element.where((entry) {
            final obsDate = DateTime.parse(entry['OBS_DT'] as String);
            return obsDate.compareTo(endDate) >= 0 && obsDate.compareTo(startDate) <= 0;
          }).toList();
          filteredData.add(tempList.toList());
        }
      }
    } catch (e, s) {
      AppLog.error(e, s);
    }

    return filteredData;
  }

  Widget _buildPeriodFilter() {
    return Builder(
      builder: (context) {
        context.watch<OfficialExperimentalDetailsBloc>();
        final hasSeries = _seriesList.any((e) => e.isNotEmpty);

        return AnimatedOpacity(
          opacity: hasSeries ? 1 : 0.3,
          duration: const Duration(milliseconds: 300),
          child: IgnorePointer(
            ignoring: !hasSeries,
            child: Container(
              decoration: BoxDecoration(
                color: !_isLightMode ? AppColors.blueShade36 : const Color(0xFFD9D9D9),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: _isLightMode ? AppColors.greyF3F4F6 : Colors.transparent,
                ),
              ),
              child: SizedBox(
                // TODO(Jerin): Change this widget to [ChartPeriodSwitcher]
                // height: 28 * textScaleFactor.value,
                height: 32,
                child: ValueListenableBuilder(
                  valueListenable: _periodOptionList,
                  builder: (context, periodOptionList, __) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(
                        periodOptionList.length,
                        (index) {
                          return InkWell(
                            onTap: () {
                              if (periodOptionList[index].isSelected) {
                                return;
                              }
                              for (int i = 0; i < periodOptionList.length; i++) {
                                periodOptionList[i].isSelected = i == index;
                              }
                              _periodOptionList.value = [...periodOptionList];
                            },
                            child: Container(
                              constraints: const BoxConstraints(minWidth: 43),
                              padding: const EdgeInsets.symmetric(horizontal: 6),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: periodOptionList[index].isSelected
                                    ? _isLightMode
                                        ? AppColors.blueLight
                                        : AppColors.blueLightOld
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(40),
                              ),
                              child: Directionality(
                                textDirection: TextDirection.ltr,
                                child: Text(
                                  periodOptionList[index].id?.toLowerCase() == LocaleKeys.recent.tr().toLowerCase()
                                      ? LocaleKeys.recent.tr()
                                      : periodOptionList[index].id?.toLowerCase() == 'all'
                                          ? LocaleKeys.all.tr()
                                          : periodOptionList[index].unit?.toLowerCase() == 'years' ||
                                                  periodOptionList[index].unit?.toLowerCase() == 'year'
                                              ? '${periodOptionList[index].value}Y'
                                              : '${periodOptionList[index].value}M',
                                  style: TextStyle(
                                    color: periodOptionList[index].isSelected
                                        ? AppColors.white
                                        : _isLightMode
                                            ? AppColors.blackShade4
                                            : AppColors.greyShade4,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildChatButton() {
    if (isDemoMode || _indicatorDetails == null) return const SizedBox();

    return IntroWidget(
      stepKey: chatButtonKey,
      stepIndex: 5,
      totalSteps: 10,
      title: LocaleKeys.chatOption.tr(),
      description: LocaleKeys.chatOptionGuideDesc.tr(),
      arrowAlignment: Alignment.bottomRight,
      isDownArrow: true,
      position: TooltipPosition.top,
      crossAxisAlignment: CrossAxisAlignment.end,
      targetBorderRadius: 50,
      onNext: () async {
        await _scrollController.scrollToWidget(context, changePresentationButtonKey, () => setState(() {}));
      },
      onPrevious: () {
        ShowCaseWidget.of(myContext!).dismiss();
        context.maybePop();
      },
      arrowPadding: const EdgeInsets.only(top: 10, right: 14, left: 14),
      targetPadding: const EdgeInsets.all(10),
      child: ChatButton<OfficialExperimentalDetailsBloc>(
        key: Key('chat.${_indicatorDetails?.id}'),
        title: widget.title,
        contentType: widget.contentType,
        indicatorDetails: _indicatorDetails!,
        screenshotController: _screenshotController,
      ),
    );
  }
}
