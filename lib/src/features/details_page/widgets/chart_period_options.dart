import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/chart_period_filter_mixin.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChartPeriodSwitcher extends StatefulWidget {
  const ChartPeriodSwitcher({
    required this.initialSelectedIndex,
    required this.options,
    required this.onChanged,
    super.key,
  });

  final int initialSelectedIndex;
  final Iterable<ChartPeriodOption> options;
  final ValueChanged<ChartPeriodOption> onChanged;

  @override
  State<ChartPeriodSwitcher> createState() => _ChartPeriodSwitcherState();
}

class _ChartPeriodSwitcherState extends State<ChartPeriodSwitcher> {
  final _isLightMode = HiveUtilsSettings.isLightMode;
  int _selectedIndex = -1;

  final _keys = <GlobalKey>[];
  double _hOffset = 0;

  @override
  void initState() {
    super.initState();

    _setKeys();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      int initialIndex = widget.initialSelectedIndex;
      if (initialIndex < 0 || initialIndex >= widget.options.length) {
        initialIndex = 0;
      }
      _onOptionTapped(widget.initialSelectedIndex);
    });
  }

  void _setKeys() {
    _keys
      ..clear()
      ..addAll(
        widget.options.map(
          (o) => GlobalObjectKey(o.id ?? o.hashCode),
        ),
      );
  }

  @override
  void didUpdateWidget(ChartPeriodSwitcher oldWidget) {
    super.didUpdateWidget(oldWidget);
    _setKeys();
  }

  void _onOptionTapped(int index) {
    if (_selectedIndex == index) return;

    int offset = 0;
    for (int i = 0; i < index; i++) {
      final box = _keys[i].currentContext?.findRenderObject() as RenderBox?;
      if (box != null) {
        offset += box.size.width.toInt();
      }
    }

    setState(() {
      _hOffset = offset.toDouble();
      _selectedIndex = index;
    });

    final option = widget.options.elementAt(index);
    widget.onChanged(option);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: !_isLightMode ? AppColors.blueShade36 : const Color(0xFFD9D9D9),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: _isLightMode ? AppColors.greyF3F4F6 : Colors.transparent,
        ),
      ),
      child: ValueListenableBuilder(
        valueListenable: HiveUtilsSettings.textSizeFactorListenable,
        builder: (context, box, child) {
          return Stack(
            children: [
              _buildPointerSwitch(),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: widget.options.indexed.map((entry) {
                  final index = entry.$1;
                  final option = entry.$2;
                  return _buildOption(index, option);
                }).toList(),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPointerSwitch() {
    if (_selectedIndex == -1) {
      return const SizedBox();
    }

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 400),
      curve: Curves.fastLinearToSlowEaseIn,
      top: 0,
      bottom: 0,
      left: HiveUtilsSettings.isLanguageEnglish ? _hOffset : null,
      right: HiveUtilsSettings.isLanguageArabic ? _hOffset : null,
      child: Container(
        constraints: const BoxConstraints(minWidth: 48),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.blueLightOld,
          borderRadius: BorderRadius.circular(30),
        ),
        alignment: Alignment.center,
        child: Text(
          _getLabelOfIndex(_selectedIndex),
          style: const TextStyle(
            color: Colors.transparent,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  String _getLabelOfIndex(int index) {
    final option = widget.options.elementAt(index);
    final id = option.id?.toLowerCase();
    final unit = option.unit?.toLowerCase();

    return id == LocaleKeys.recent.toLowerCase()
        ? LocaleKeys.recent.tr()
        : id == LocaleKeys.all
            ? LocaleKeys.all.tr()
            : unit?.toLowerCase() == 'years' || unit?.toLowerCase() == 'year'
                ? '${option.value}Y'
                : '${option.value}M';
  }

  Widget _buildOption(int index, ChartPeriodOption option) {
    final isSelected = index == _selectedIndex;

    final label = _getLabelOfIndex(index);

    final textColor = isSelected
        ? AppColors.white
        : _isLightMode
            ? AppColors.blackShade4
            : AppColors.greyShade4;

    final key = _keys.elementAt(index);

    return InkWell(
      onTap: () => _onOptionTapped(index),
      child: Container(
        key: key,
        constraints: const BoxConstraints(minWidth: 48),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        alignment: Alignment.center,
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: Text(
            label,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }
}
