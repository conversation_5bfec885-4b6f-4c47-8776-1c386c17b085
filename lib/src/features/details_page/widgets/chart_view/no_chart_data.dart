import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class NoChartData extends StatelessWidget {
  const NoChartData({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      constraints: const BoxConstraints(
        minHeight: 200,
      ),
      child: Text(
        LocaleKeys.filterEmptyMessage.tr(),
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: HiveUtilsSettings.isLightMode ? AppColors.black : AppColors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
