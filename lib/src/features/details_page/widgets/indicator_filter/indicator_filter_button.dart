import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/indicator_filter_adapter_mixin.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_filter/indicator_filter_bottom_sheet.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class IndicatorFilterButton extends StatelessWidget {
  const IndicatorFilterButton({
    required this.adapter,
    required this.onFilterApplied,
    super.key,
  });

  final IndicatorFilterAdapterMixin adapter;
  final ValueChanged<SelectedFilterMap> onFilterApplied;

  Future<void> _onFilterPressed(BuildContext context) async {
    await IndicatorFilterBottomSheet.show(
      context,
      initialFilter: adapter.selectedFilterMap,
      filterPanel: adapter.filterPanel!,
      onFilterApplied: onFilterApplied,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final count = adapter.filterCount;

    return Material(
      elevation: 4,
      shadowColor: AppColors.grey.withValues(alpha: 0.2),
      color: isLightMode ? Colors.white : AppColors.blueShade36,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
        side: BorderSide(
          color: isLightMode ? Colors.transparent : AppColors.blueShade36,
        ),
      ),
      child: InkWell(
        onTap: () => _onFilterPressed(context),
        borderRadius: BorderRadius.circular(30),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.icFilters,
                colorFilter: ColorFilter.mode(
                  isLightMode ? AppColors.blueShade22 : AppColors.white,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 10),
              Text(
                LocaleKeys.filters.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (count > 0)
                Center(
                  child: Container(
                    margin: HiveUtilsSettings.isLanguageEnglish
                        ? const EdgeInsets.only(left: 10)
                        : const EdgeInsets.only(right: 10),
                    padding: const EdgeInsets.symmetric(horizontal: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      color: AppColors.red,
                    ),
                    child: Text(
                      count.toString(),
                      style: const TextStyle(color: AppColors.white, fontSize: 12),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
