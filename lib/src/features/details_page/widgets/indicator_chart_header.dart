import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_classification.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_type_tag.dart';

class IndicatorChartHeader extends StatelessWidget {
  const IndicatorChartHeader({
    required this.tag,
    required this.fullScreenButtonBuilder,
    this.filterBuilder,
    this.driverBuilder,
    this.security,
    super.key,
  });

  final WidgetBuilder? filterBuilder;
  final WidgetBuilder? driverBuilder;
  final Security? security;
  final IndicatorTypeTag tag;
  final WidgetBuilder fullScreenButtonBuilder;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildFilterOrChangeDriverButton(context),
            const Spacer(),
            IndicatorClassification(security: security),
          ],
        ),
        const Sized<PERSON>ox(height: 14),
        Row(
          children: [
            tag,
            const Spacer(),
            const SizedBox(width: 12),
            fullScreenButtonBuilder(context),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterOrChangeDriverButton(BuildContext context) {
    if (filterBuilder != null) {
      return filterBuilder!(context);
    }

    if (driverBuilder != null) {
      return driverBuilder!(context);
    }

    return const SizedBox.shrink();
  }
}
