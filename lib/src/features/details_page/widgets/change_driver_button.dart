import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChangeDriverButton extends StatelessWidget {
  const ChangeDriverButton({
    required this.onPressed,
    this.count,
    this.alignment = Alignment.centerLeft,
    super.key,
  });

  final VoidCallback onPressed;
  final int? count;
  final Alignment alignment;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Material(
      elevation: 4,
      color: isLightMode ? Colors.white : AppColors.blueShade36,
      shadowColor: AppColors.grey.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
        side: BorderSide(
          color: isLightMode ? Colors.transparent : AppColors.blueShade36,
        ),
      ),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(30),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.icChangeDriver,
                colorFilter: isLightMode
                    ? const ColorFilter.mode(
                        AppColors.blueShade22,
                        BlendMode.srcIn,
                      )
                    : const ColorFilter.mode(
                        AppColors.white,
                        BlendMode.srcIn,
                      ),
              ),
              const SizedBox(width: 10),
              Text(
                LocaleKeys.changeDrivers.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (count != null)
                Center(
                  child: Container(
                    margin: const EdgeInsets.only(
                      left: 10,
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      color: AppColors.red,
                    ),
                    child: Text(
                      '$count',
                      style: const TextStyle(color: AppColors.white, fontSize: 12),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
