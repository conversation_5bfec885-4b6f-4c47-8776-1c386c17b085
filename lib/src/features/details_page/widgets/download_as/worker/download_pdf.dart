// context are synchronous
// ignore_for_file: use_build_context_synchronously
part of '../download_as.dart';

const _kAspectRatio = 1.4142;
const _kA4AspectRatio = 1 / _kAspectRatio; // A4 paper aspect ratio (210mm x 297mm)
const _kPixelRatio = _kAspectRatio * 2; // High pixel ratio for better quality

Widget _getDecoratedPage(BuildContext context, Size size, List<Widget> children) {
  //  This widget seems to be rendered in a different context
  //  Hence, explicitly passing MediaQuery data for text size factors.
  return SizedBox(
    width: size.width,
    child: AspectRatio(
      aspectRatio: _kA4AspectRatio,
      child: MediaQuery(
        data: MediaQuery.of(context),
        child: DecoratedBox(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.grey),
            color: AppColors.white,
          ),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: children,
                ),
              ),
              Container(
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: AppColors.grey),
                  ),
                ),
                child: const _CopyrightFooter(),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

Future<PdfDocument> _buildDocument(_DownloadAsPreviewDialogState state) async {
  final document = PdfDocument();
  PdfPage pdfPage = document.pages.add();
  final clientSize = pdfPage.getClientSize();

  final items = [
    state._logoKey,
    state._titleKey,
    state._descriptionKey,
    state._chartKey,
  ];

  final deviceWidth = MediaQuery.sizeOf(state.context).width;

  //  PDF page is rendered with a maximum width of the device for small screens.
  final renderWidth = min(deviceWidth, clientSize.width);

  //  Max size of a PDF page with A4 aspect ratio
  final pageSize = Size(
    renderWidth,
    renderWidth * _kAspectRatio,
  );

  final footerHeight = (state._footerKey.currentContext!.findRenderObject()! as RenderBox).size.height;
  final maxContentHeight = pageSize.height - footerHeight;

  final pages = <Widget>[];
  final children = <Widget>[];

  //  High pixel ratio for better quality
  double cumulativeSize = 0;
  for (final key in items) {
    final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
    Widget? widget = key.currentWidget;
    if (widget == null) continue;

    final widgetSize = renderBox?.size.height ?? 0;

    if (key == state._chartKey) {
      //  explicitly capture screenshot of chart, since context in widget
      //  may misbehave while using in _getDecoratedPage()
      final chartBytes = await state._chartScreenshotController.capture(
        pixelRatio: _kPixelRatio,
        delay: const Duration(milliseconds: 500),
      );
      if (chartBytes == null) continue;

      final k = GlobalKey();
      widget = SizedBox(
        width: clientSize.width,
        height: widgetSize,
        child: Image.memory(
          key: k,
          chartBytes,
          fit: BoxFit.contain,
        ),
      );
    }

    if ((cumulativeSize + widgetSize) > maxContentHeight) {
      pages.add(
        _getDecoratedPage(
          state.context,
          pageSize,
          List.from(children),
        ),
      );
      children.clear();
      cumulativeSize = 0;
    }

    children.addAll([
      const SizedBox(height: 10),
      widget,
    ]);

    cumulativeSize += widgetSize + 10;
    if (key == state._logoKey) {
      children.add(
        const Divider(height: 16),
      );
      cumulativeSize += 16;
    }
  }

  //  Add all pending children into list
  if (children.isNotEmpty) {
    pages.add(
      _getDecoratedPage(
        state.context,
        pageSize,
        List.from(children),
      ),
    );
  }

  //  Popping the dialog before taking the screenshot to avoid duplicate global key exception.
  Navigator.pop(state.context);
  await Future<void>.delayed(
    const Duration(milliseconds: 200),
  );

  final screenshotController = ScreenshotController();
  //  PDF Generation
  for (final decoratedPage in pages) {
    final bytes = await screenshotController.captureFromWidget(
      decoratedPage,
      pixelRatio: _kPixelRatio,
    );
    pdfPage.graphics.drawImage(
      PdfBitmap(bytes),
      Rect.fromLTWH(0, 0, clientSize.width, clientSize.height),
    );

    if (pages.last != decoratedPage) {
      pdfPage = document.pages.add();
    }
  }

  return document;
}

Future<void> _savePdf(_DownloadAsPreviewDialogState state, String title) async {
  final notification = AppMessage.showOverlayNotificationSuccess(
    message: LocaleKeys.downloading.tr(),
  );

  try {
    final document = await _buildDocument(state);
    final documentAsBytes = document.saveSync();
    document.dispose();

    await FileUtils.saveFileAndNotify(
      bytes: documentAsBytes,
      filePath: FileUtils.joinPathParts(
        [FileUtils.dirPathIndicator, 'Indicator $title.pdf'],
      ),
    );
  } catch (e, st) {
    debugPrint('Failed to save PDF: $e\n$st');
    Completer<void>().completeError(e);
    AppMessage.showOverlayNotificationError(
      message: LocaleKeys.somethingWentWrong.tr(),
    );
  }

  notification?.dismiss(animate: false);
}
