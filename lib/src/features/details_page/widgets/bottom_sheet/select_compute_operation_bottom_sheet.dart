import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/app_radio_tile.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/features/details_page/compute/data/enums/compute_operations.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class SelectComputeOperationBottomSheet extends StatefulWidget {
  const SelectComputeOperationBottomSheet({
    this.initialOperation,
    this.onOperationSelected,
    super.key,
  });

  final ComputeOperations? initialOperation;
  final ValueChanged<ComputeOperations>? onOperationSelected;

  static Future<ComputeOperations?> show(
    BuildContext context, {
    ComputeOperations? initialOperation,
    ValueChanged<ComputeOperations>? onOperationSelected,
  }) async {
    return showModalBottomSheet<ComputeOperations?>(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return SelectComputeOperationBottomSheet(
          initialOperation: initialOperation,
          onOperationSelected: onOperationSelected,
        );
      },
    );
  }

  @override
  State<SelectComputeOperationBottomSheet> createState() => _SelectComputeOperationBottomSheetState();
}

class _SelectComputeOperationBottomSheetState extends State<SelectComputeOperationBottomSheet> {
  late ComputeOperations _selectedOperation = widget.initialOperation ?? ComputeOperations.summation;

  final _isLightMode = HiveUtilsSettings.isLightMode;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      decoration: BoxDecoration(
        color: _isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          const BottomSheetTopNotch(),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.computeData.tr(),
            style: TextStyle(
              color: _isLightMode ? AppColors.blackShade1 : AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 20),
          ...ComputeOperations.values.map(
            (e) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: AppRadioTile(
                title: e.label,
                isSelected: e == _selectedOperation,
                trailingIcon: e.assetFile,
                onTap: () => setState(() => _selectedOperation = e),
              ),
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: const Size.fromHeight(43),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            onPressed: () {
              Navigator.pop(context, _selectedOperation);
              widget.onOperationSelected?.call(_selectedOperation);
            },
            child: Text(
              LocaleKeys.next.tr(),
              style: const TextStyle(
                color: AppColors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
