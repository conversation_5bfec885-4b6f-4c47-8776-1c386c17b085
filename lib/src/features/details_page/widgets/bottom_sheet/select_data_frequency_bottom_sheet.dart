import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/app_radio_tile.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

enum ChartDataFrequency {
  yearly('Yearly', 'سنوي'),
  monthly('Monthly', 'شهري'),
  quarterly('Quarterly', 'ربع سنوي');

  const ChartDataFrequency(this.en, this.ar);

  final String en;
  final String ar;

  List<String> get translations => [en, ar];

  String get label => HiveUtilsSettings.isLanguageArabic ? ar : en;

  static ChartDataFrequency fromString(String value) {
    return ChartDataFrequency.values.firstWhere(
      (e) => e.en == value || e.ar == value,
      orElse: () => ChartDataFrequency.monthly,
    );
  }
}

class SelectDataFrequencyBottomSheet extends StatefulWidget {
  const SelectDataFrequencyBottomSheet._({
    required this.initialFrequency,
    required this.availableFrequencyOptions,
    required this.onPreview,
  });

  final ChartDataFrequency initialFrequency;
  final Iterable<ChartDataFrequency> availableFrequencyOptions;
  final ValueChanged<ChartDataFrequency> onPreview;

  static Future<dynamic> show(
    BuildContext context, {
    required ValueChanged<ChartDataFrequency> onPreview,
    required ValueChanged<ChartDataFrequency> onDone,
    required List<String> timeUnits,
    ChartDataFrequency? initialFrequency,
  }) async {
    if (timeUnits.isEmpty) return;

    final options = timeUnits
        .map(
          (unit) => ChartDataFrequency.values.firstWhere(
            (e) => e.en == unit,
          ),
        )
        .toSet();

    final initial = initialFrequency ?? options.first;
    final result = await showModalBottomSheet<ChartDataFrequency>(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) => SelectDataFrequencyBottomSheet._(
        initialFrequency: initial,
        availableFrequencyOptions: options,
        onPreview: onPreview,
      ),
    );

    onDone.call(result ?? initial);
  }

  @override
  State<SelectDataFrequencyBottomSheet> createState() => _SelectDataFrequencyBottomSheetState();
}

class _SelectDataFrequencyBottomSheetState extends State<SelectDataFrequencyBottomSheet> {
  late ChartDataFrequency _selectedFrequency = widget.initialFrequency;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          const BottomSheetTopNotch(),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.changeDataFrequency.tr(),
            style: TextStyle(
              color: isLightMode ? AppColors.blackShade1 : AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 20),
          Column(
            spacing: 16,
            children: widget.availableFrequencyOptions.map(
              (e) {
                return AppRadioTile(
                  title: e.label,
                  isSelected: e == _selectedFrequency,
                  onTap: () {
                    setState(() => _selectedFrequency = e);
                    widget.onPreview.call(e);
                  },
                );
              },
            ).toList(),
          ),
          const SizedBox(height: 15),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: const Size.fromHeight(43),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            onPressed: () => Navigator.pop(context, _selectedFrequency),
            child: Text(
              LocaleKeys.done.tr(),
              style: const TextStyle(
                color: AppColors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
