import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_status_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/pages/add_to_myapps.dart';
import 'package:scad_mobile/src/features/notification/data/models/request/subscription_request.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class NotificationAndMyAppButton extends StatefulWidget {
  const NotificationAndMyAppButton({
    super.key,
    this.indicatorDetails,
    this.contentType,
    this.isComparisonActive = false,
  });
  final IndicatorDetailsResponseHelper? indicatorDetails;
  final String? contentType;
  final bool isComparisonActive;

  @override
  State<NotificationAndMyAppButton> createState() => _NotificationAndMyAppButtonState();
}

class _NotificationAndMyAppButtonState extends State<NotificationAndMyAppButton> {
  NodeIdUuid? subscription ;
  NodeIdUuid? myApps;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        if (mounted) {
          context.read<IndicatorCardBloc>().add(
                GetIndicatorStatusEvent(
                  id: widget.indicatorDetails!.indicatorDetails.id!,
                ),
              );
        }
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
        // error
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isDemoMode) {
      return const SizedBox();
    }
    final isLightMode = HiveUtilsSettings.isLightMode;
    return BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
      listener: (context, state) {
        if (state is IndicatorStatusErrorState) {
          subscription = NodeIdUuid();
          myApps = NodeIdUuid();
        } else if (state is IndicatorStatusSuccessState) {
          subscription = state.subscription;
          myApps = state.myApps;
        }
      },
      builder: (context, state) {
        // api is not available for app to get compared indicator details
        // from my apps
        if (widget.indicatorDetails?.indicatorDetails.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.id ==
            'compute-data') {
          return const SizedBox();
        }

        final indicatorId = widget.indicatorDetails!.indicatorDetails.id ?? '';

        return Row(
          children: [
            if (!widget.isComparisonActive)
              BlocConsumer<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  if (state is SubscriptionSuccessState) {
                    if (state.id == widget.indicatorDetails!.indicatorDetails.id!) {
                      if (mounted) {
                        context.read<IndicatorCardBloc>().add(
                              GetIndicatorStatusEvent(
                                id: widget.indicatorDetails!.indicatorDetails.id!,
                              ),
                            );
                      }
                    }
                  }
                },
                builder: (context, state) {
                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        if (subscription?.nodeId == null) {
                          context.read<NotificationBloc>().add(
                                CreateSubscriptionEvent(
                                  indicatorId: indicatorId,
                                  title: widget.indicatorDetails!.indicatorDetails.componentTitle ?? '',
                                  request: SubscriptionRequest(
                                    nodeId: widget.indicatorDetails!.indicatorDetails.id,
                                    appType: widget.indicatorDetails?.indicatorDetails.type,
                                    contentType: widget.contentType,
                                  ),
                                ),
                              );
                        } else {
                          context.read<NotificationBloc>().add(
                                RemoveSubscriptionEvent(
                                  indicatorId: indicatorId,
                                  title: widget.indicatorDetails!.indicatorDetails.componentTitle ?? '',
                                  uuid: subscription!.uuid!,
                                  id: subscription!.nodeId!,
                                ),
                              );
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: subscription?.nodeId != null
                            ? SvgPicture.asset(
                                isLightMode ? AppImages.icBellSolidLight : AppImages.icBellSolid,
                              )
                            : SvgPicture.asset(
                                AppImages.icBell,
                                colorFilter: ColorFilter.mode(
                                  isLightMode ? AppColors.greyShade4 : AppColors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                      ),
                    ),
                  );
                },
              ),
            BlocConsumer<MyAppsBloc, MyAppsState>(
              listener: (context, state) {
                if (state is MyAppsStatusSuccessResponseState) {
                  if (state.id == widget.indicatorDetails!.indicatorDetails.id!) {
                    if (mounted) {
                      context.read<IndicatorCardBloc>().add(
                            GetIndicatorStatusEvent(
                              id: widget.indicatorDetails!.indicatorDetails.id!,
                            ),
                          );
                    }
                  }
                }
              },
              builder: (context, state) {
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(
                      20,
                    ),
                    onTap: () async {
                      if (myApps?.nodeId == null) {
                        // add to my apps
                        await showModalBottomSheet<void>(
                          isScrollControlled: true,
                          useRootNavigator: true,
                          constraints: BoxConstraints(
                            minHeight: 100,
                            maxHeight: MediaQuery.sizeOf(context).height * .90,
                          ),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(
                                20,
                              ),
                            ),
                          ),
                          backgroundColor: Colors.transparent,
                          context: context,
                          builder: (context) {
                            return AddToMyApps(
                              indicatorDetails: widget.indicatorDetails!,
                              nodeId: widget.indicatorDetails!.indicatorDetails.id!,
                              contentType: widget.contentType ?? widget.indicatorDetails?.indicatorDetails.indicatorType ?? '',
                              isComparisonActive: widget.isComparisonActive,
                            );
                          },
                        );
                      } else {
                        context.read<MyAppsBloc>().add(
                              RemoveFromMyAppsEvent(
                                indicatorId: indicatorId,
                                title: widget.indicatorDetails!.indicatorDetails.componentTitle ?? '',
                                id: widget.indicatorDetails!.indicatorDetails.id!,
                                uuid: myApps!.uuid!,
                              ),
                            );
                      }
                    },
                    child: Container(
                        padding: const EdgeInsets.all(8),
                        child: myApps?.nodeId != null
                            ? isLightMode
                                ? SvgPicture.asset(
                                    AppImages.icAddToMyAppsOnLight,
                                  )
                                : SvgPicture.asset(
                                    AppImages.icAddToMyAppsOn,
                                  )
                            : SvgPicture.asset(AppImages.icAddToMyAppsOff,
                                colorFilter: ColorFilter.mode(
                                  isLightMode
                                      ? AppColors.greyShade4
                                      : AppColors.white,
                                  BlendMode.srcIn,
                                ))),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
