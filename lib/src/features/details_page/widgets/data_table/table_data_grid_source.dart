import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class TableDataGridSource extends DataGridSource {
  TableDataGridSource({
    required this.tableData,
    required this.columnNames,
  }) {
    /// the below line is to hide the legend names from the table
    columnNames..removeWhere((dt) => dt == 'legend_name')
    ..removeWhere((bt) => bt == 'legend_title');

    dataGridRows = tableData.map<DataGridRow>((employeeData) {
      return DataGridRow(
        cells: columnNames.map<DataGridCell>((columnName) {
          final value = employeeData[columnName];
          return DataGridCell<dynamic>(
            columnName: (HiveUtilsSettings.isLanguageEnglish ? _titleMapEn : _titleMapAr)[columnName] ?? columnName,
            value: value ?? '-',
          );
        }).toList(),
      );
    }).toList();
  }

   List<Map<String, dynamic>> tableData;
   List<String> columnNames;

  Map<String, String> get _titleMapAr => {
    'INDICATOR_ID': 'معرف المؤشر',
    'RUN_SEQ_ID': 'تشغيل معرف التسلسل',
    'RUN_DT': 'تشغيل التاريخ',
    'VALUE': 'قيمة',
    'VALUE_LL': 'الحد الأدنى للقيمة',
    'VALUE_UL': 'الحد الأعلى للقيمة',
    'UNIT': 'وحدة',
    'OBS_DT': 'تاريخ المراقبة',
    'OPT': 'علم التوقعات',
    'TYPE': 'يكتب',
    'OIL_NONOIL': 'فئة',
    'SECTOR': 'قطاع',
    'INDUSTRY': 'صناعة',
    'PARAMETER_COMBO_ID': 'معرف التحرير والسرد المعلمة',
    'SECTOR_AR': 'القطاع العربي',
    'VALUE_FORECAST': 'القيمة المتوقعة',
    'OBS_DT_CUR': 'تاريخ المراقبة الحقيقي',
    'VALUE_PERC_ECO': 'النسبة المئوية للقيمة',
    'VALUE_CURRENT': 'القيمة الحقيقية',
    'CHANGE': 'يتغير',
    'CHANGE_PY': 'نسبة التغيير',
    'LANGUAGE_CD': 'نوع اللغة',
    'NATIONALITY_TYPE': 'نوع الجنسية',
  };

   Map<String, String> get _titleMapEn => {
    'INDICATOR_ID': 'Indicator ID',
    'VALUE': 'Value',
    'VALUE_LL': 'Value Upper Limit',
    'VALUE_UL': 'Value Lower Limit',
    'UNIT': 'Unit',
    'OBS_DT': 'Observation Date',
    'OPT': 'OPT',
    'TYPE': 'Type',
    'OIL_NONOIL': 'Oil/Non Oil',
    'SECTOR': 'Sector',
    'INDUSTRY': 'Industry',
    'PARAMETER_COMBO_ID': 'Parameter Combo ID',
    'SECTOR_AR': 'Sector Arabic',
    'VALUE_FORECAST': 'Value Forecast',
    'OBS_DT_CUR': 'Current Observation Date',
    'VALUE_PERC_ECO': 'Value Percentage',
    'VALUE_CURRENT': 'Current Value',
    'CHANGE': 'Change',
    'NATIONALITY_TYPE': 'Nationality Type',
    'CHANGE_PY': 'Change Percentage',
  };

  List<DataGridRow> dataGridRows = [];

  @override
  List<DataGridRow> get rows => dataGridRows;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final index = dataGridRows.indexOf(row);

    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((dataGridCell) {
        return Container(
          alignment: /*dataGridCell.value.runtimeType == double ||
                  dataGridCell.value == '-'
              ? Alignment.center
              :*/
              Alignment.centerLeft,
          color: (index % 2 != 0) ? AppColors.greyShade7 : Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            dataGridCell.value.runtimeType == double
                ? (dataGridCell.value as double).toStringAsFixed(2)
                : dataGridCell.value.toString(),
            style: AppTextStyles.s10w5cWhite.copyWith(
              color: isLightMode
                  ? AppColors.black
                  : index.isEven
                      ? AppColors.white
                      : AppColors.black,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
    );
  }
}
