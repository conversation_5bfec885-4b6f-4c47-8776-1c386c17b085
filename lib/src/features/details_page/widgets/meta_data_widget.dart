import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart' hide MetaData;
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image/image.dart' as img;
import 'package:scad_mobile/src/common/widgets/custom_animated_expansion_tile.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/app_permission.dart';
import 'package:scad_mobile/src/utils/app_utils/file_utils.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' hide Column;

class MetaDataWidget extends StatefulWidget {
  const MetaDataWidget({
    required this.id,
    required this.title,
    this.metaData,
    this.margin = EdgeInsets.zero,
    super.key,
  });

  final List<MetaData?>? metaData;
  final String id;
  final String title;
  final EdgeInsets margin;

  @override
  State<MetaDataWidget> createState() => _MetaDataWidgetState();
}

class _MetaDataWidgetState extends State<MetaDataWidget> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  final _metaDataList = <MetaData>[];
  bool _isOpened = false;
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();

    _metaDataList.addAll(
      List<MetaData>.from(widget.metaData?.where((e) => e != null) ?? <MetaData>[]),
    );
  }

  @override
  void didUpdateWidget(MetaDataWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.metaData != widget.metaData) {
      setState(() {
        _metaDataList
          ..clear()
          ..addAll(
            List<MetaData>.from(widget.metaData?.where((e) => e != null) ?? <MetaData>[]),
          );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_metaDataList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: widget.margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: _isLightMode ? AppColors.white : AppColors.blueShade36,
      ),
      child: CustomAnimatedExpansionTile(
        isExpanded: _isOpened,
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
        title: LocaleKeys.metaData.tr(),
        leading: SvgPicture.asset(
          'assets/images/metadata.svg',
          colorFilter: ColorFilter.mode(
            _isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
            BlendMode.srcIn,
          ),
        ),
        trailing: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(50),
          child: InkWell(
            borderRadius: BorderRadius.circular(50),
            onTap: _onDownloadTapped,
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: Icon(
                Icons.download_rounded,
                color: _isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
              ),
            ),
          ),
        ),
        onExpansionChanged: () => setState(
          () => _isOpened = !_isOpened,
        ),
        children: [
          if (_isOpened)
            const Divider(
              color: AppColors.greyShade1,
            ),
          ...List.generate(_metaDataList.length, (index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _metaDataList[index].label ?? '',
                    style: TextStyle(
                      color: _isLightMode ? AppColors.blackTextTile : AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _metaDataList[index].value ?? '-',
                    style: TextStyle(
                      color: _isLightMode ? AppColors.grey : AppColors.greyShade4,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 15),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Future<void> _onDownloadTapped() async {
    if (_isDownloading) {
      return;
    }

    _isDownloading = true;
    await _saveAsExcel(context);
    _isDownloading = false;
  }

  Future<void> _saveAsExcel(BuildContext context) async {
    final bool hasPermission = await AppPermissions.checkPermissions(
      context,
      [AppPermission.storageAndroid],
    );
    if (!hasPermission) {
      return;
    }

    AppMessage.showOverlayNotificationSuccess(message: LocaleKeys.downloading.tr());

    final Workbook workbook = Workbook();

    final Worksheet sheet = workbook.worksheets[0]..showGridlines = true;

    //  Add Bayaan logo
    final bayaanLogo = await _getLogoAsBytes(AppImages.logoBayaanHorizontalPng, 50, 124);
    final bayaanLogoAsBytes = bayaanLogo.buffer.asUint8List();
    sheet.pictures.addStream(1, 1, bayaanLogoAsBytes);
    sheet.getRangeByName('A1:A3').merge();

    //  Add SCAD logo
    final scadLogo = await _getLogoAsBytes(AppImages.scadLogoHorz, 50, 140);
    final scadLogoAsBytes = scadLogo.buffer.asUint8List();
    sheet.pictures.addStream(1, 2, scadLogoAsBytes);
    sheet.getRangeByName('B1:B3').merge();

    //  Empty Row
    sheet.getRangeByName('A4:B4').merge();

    //  Title Row
    sheet.getRangeByName('A5:B5').merge();

    final Style globalStyle1 = workbook.styles.add('style1')
      ..backColor = '#D09A57'
      ..fontName = 'Arial'
      ..fontSize = 10
      ..fontColor = '#FFFFFF'
      ..bold = true
      ..hAlign = HAlignType.center
      ..vAlign = VAlignType.center;
    globalStyle1.borders.all.lineStyle = LineStyle.thin;
    globalStyle1.borders.all.color = '#000000';

    final Style globalStyle2 = workbook.styles.add('style2')
      ..backColor = '#D6FEE7'
      ..fontName = 'Calibri (Body)'
      ..fontSize = 11
      ..fontColor = '#000000'
      ..hAlign = HAlignType.left
      ..vAlign = VAlignType.center;
    globalStyle2.borders.all.lineStyle = LineStyle.thin;
    globalStyle2.borders.all.color = '#000000';

    final Style globalStyle3 = workbook.styles.add('style3')
      ..fontName = 'Calibri (Body)'
      ..fontSize = 11
      ..fontColor = '#000000'
      ..hAlign = HAlignType.left
      ..vAlign = VAlignType.center;

    final Style globalStyle4 = workbook.styles.add('style4')
      ..fontName = 'Arial'
      ..fontSize = 14
      ..fontColor = '#000000'
      ..bold = true
      ..hAlign = HAlignType.left
      ..vAlign = VAlignType.center;

    sheet.getRangeByName('A5')
      ..setText(widget.title)
      ..cellStyle = globalStyle4
      ..cellStyle.hAlign = HAlignType.center;

    sheet.getRangeByName('A6')
      ..setText('Label')
      ..cellStyle = globalStyle1;

    sheet.getRangeByName('B6')
      ..setText('Value')
      ..cellStyle = globalStyle1;

    int startRow = 7;

    for (final MetaData? field in _metaDataList) {
      if (field == null) continue;

      sheet.getRangeByName('A$startRow')
        ..setText(field.label ?? '')
        ..cellStyle = globalStyle3
        ..autoFitColumns();

      sheet.getRangeByName('B$startRow')
        ..setText(field.value ?? '')
        ..cellStyle = globalStyle3
        ..autoFitColumns();

      startRow++;
    }

    sheet.getRangeByName('A$startRow:B$startRow').merge();

    sheet.getRangeByName('A$startRow')
      ..setText(LocaleKeys.sensitiveInformation.tr())
      ..cellStyle = globalStyle2;

    sheet
      ..autoFitRow(1)
      ..autoFitColumn(1)
      ..autoFitColumn(2);

    final List<int> bytes = workbook.saveSync();
    workbook.dispose();

    await FileUtils.saveFileAndNotify(
      bytes: bytes,
      filePath: FileUtils.joinPathParts([FileUtils.dirPathMetadata, 'Meta Data & Methodology ${widget.id}.xlsx']),
    );
  }

  Future<ByteData> _getLogoAsBytes(String imageAssetPath, int height, int width) async {
    final ByteData assetImageByteData = await rootBundle.load(imageAssetPath);
    final img.Image? baseSizeImage = img.decodeImage(assetImageByteData.buffer.asUint8List());
    final img.Image resizeImage = img.copyResize(baseSizeImage!, height: height, width: width);
    final ui.Codec codec = await ui.instantiateImageCodec(img.encodePng(resizeImage));
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ByteData? byteData = await frameInfo.image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!;
  }
}
