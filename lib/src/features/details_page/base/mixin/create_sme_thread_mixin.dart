import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_create_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/domain/repositories/chat_with_sme_repository_imports.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/new_chat_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class CreateSMEThreadEvent extends DetailsBaseEvent {
  const CreateSMEThreadEvent({
    required this.indicatorDetails,
    required this.contentType,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final String contentType;

  @override
  List<Object?> get props => [indicatorDetails, contentType];
}

abstract class CreateSMEThreadEventBaseState extends DetailsBaseState {
  const CreateSMEThreadEventBaseState();
}

class CreateSMEThreadSuccessState extends CreateSMEThreadEventBaseState {
  const CreateSMEThreadSuccessState({
    required this.chatThread,
    required this.domain,
    required this.domainId,
    required this.theme,
    required this.subTheme,
    required this.indicatorNodeId,
    required this.indicatorAppType,
    required this.indicatorContentType,
    required this.indicatorKey,
    required this.indicatorName,
    this.chatDisabled,
    this.chatThreadClosed,
  });

  final ChatThreadCreateModel? chatThread;
  final String? domain;
  final int? domainId;
  final String? theme;
  final String? subTheme;
  final String? indicatorNodeId;
  final String? indicatorAppType;
  final String? indicatorContentType;
  final String? indicatorKey;
  final String? indicatorName;
  final bool? chatDisabled;
  final bool? chatThreadClosed;

  @override
  List<Object> get props => [chatThread ?? ChatThreadCreateModel()];
}

class CreateSMEThreadErrorState extends CreateSMEThreadEventBaseState {
  const CreateSMEThreadErrorState({required this.error});

  final String? error;

  @override
  List<Object> get props => [error ?? ''];
}

mixin CreateSMEThreadMixin on DetailsBaseBloc {
  void onCreateSMEThreadListener() {
    on<CreateSMEThreadEvent>(_onCreateThreadEvent);
  }

  Future<void> _onCreateThreadEvent(CreateSMEThreadEvent event, Emitter<DetailsBaseState> emit) async {
    try {
      final List<String> multiDomainId = HiveUtilsAuth.getJWTDetails().multiDomainId ?? [];

      final domain = event.indicatorDetails.domain ?? '';
      final domainId = event.indicatorDetails.domainId ?? '';
      final theme = event.indicatorDetails.theme ?? '';
      final subTheme = event.indicatorDetails.subtheme ?? '';
      final indicatorNodeId = event.indicatorDetails.id ?? '';
      final indicatorAppType = event.indicatorDetails.indicatorType ?? '';
      final indicatorContentType = event.contentType;
      final indicatorKey = event.indicatorDetails.contentClassificationKey ?? '';
      final indicatorName = event.indicatorDetails.componentTitle ?? '';

      if (multiDomainId.contains(domainId)) {
        bool allowCreate = false;

        final chatStatusResponse =
            await servicelocator<IndicatorCardRepository>().getIndicatorStatus(id: indicatorNodeId);

        if (chatStatusResponse.isSuccess) {
          if (chatStatusResponse.response?.chatThreadExist ?? false) {
            allowCreate = true;
          } else {
            allowCreate = await NewChatBottomSheet.createChatThreadWarning(
              servicelocator<AppRouter>().navigatorKey.currentContext!,
              domainId,
            );
          }
        } else {
          emit(
            CreateSMEThreadErrorState(
              error: LocaleKeys.somethingWentWrong.tr(),
            ),
          );
          return;
        }

        if (!allowCreate) return;
      }

      final RepoResponse<ChatThreadCreateModel> response =
          await servicelocator<ChatWithSmeRepository>().createChatThreadFromDetailsPage(
        domain: domain,
        domainId: int.parse(domainId),
        theme: theme,
        subTheme: subTheme,
        subject: domain,
        indicatorNodeId: indicatorNodeId,
        indicatorAppType: indicatorAppType,
        indicatorContentType: indicatorContentType,
        indicatorKey: indicatorKey,
        indicatorName: indicatorName,
      );

      if (response.isSuccess) {
        emit(
          CreateSMEThreadSuccessState(
            chatThread: response.response,
            domain: domain,
            domainId: int.parse(domainId),
            theme: theme,
            subTheme: subTheme,
            indicatorNodeId: indicatorNodeId,
            indicatorAppType: indicatorAppType,
            indicatorContentType: indicatorContentType,
            indicatorKey: indicatorKey,
            indicatorName: indicatorName,
            chatDisabled: response.response?.chatDisabled,
            chatThreadClosed: response.response?.chatThreadClosed,
          ),
        );
      } else {
        emit(
          CreateSMEThreadErrorState(
            error: response.errorMessage.isNotEmpty ? response.errorMessage : 'Chat creation failed!',
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        CreateSMEThreadErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }
}
