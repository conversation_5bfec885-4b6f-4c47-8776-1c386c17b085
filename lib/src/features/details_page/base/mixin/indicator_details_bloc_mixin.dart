import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class GetIndicatorDetailsEvent extends DetailsBaseEvent {
  const GetIndicatorDetailsEvent({required this.id, required this.contentType});

  final String id;

  final String contentType;

  @override
  List<Object?> get props => [id, contentType];
}

abstract class GetIndicatorDetailsBaseState extends DetailsBaseState {
  const GetIndicatorDetailsBaseState();
}

class GetIndicatorDetailsLoadingState extends GetIndicatorDetailsBaseState {}

class GetIndicatorDetailsErrorState extends GetIndicatorDetailsBaseState {
  const GetIndicatorDetailsErrorState({required this.error});

  final String error;

  @override
  List<Object?> get props => [error];
}

class GetIndicatorDetailsSuccessState extends GetIndicatorDetailsBaseState {
  const GetIndicatorDetailsSuccessState({required this.indicatorDetails});

  final IndicatorDetailsResponse indicatorDetails;

  @override
  List<Object?> get props => [indicatorDetails];
}

mixin IndicatorDetailsBlocMixin on DetailsBaseBloc {
  void onGetIndicatorDetailsListener() {
    on<GetIndicatorDetailsEvent>(_getIndicatorDetails);
  }

  Future<void> _getIndicatorDetails(GetIndicatorDetailsEvent event, Emitter<DetailsBaseState> emit) async {
    try {
      emit(GetIndicatorDetailsLoadingState());

      final RepoResponse<IndicatorDetailsResponse> indicatorDetailsResponse =
          await servicelocator<IndicatorCardRepository>().indicatorDetails(
        id: event.id,
        contentType: event.contentType,
      );

      if (!indicatorDetailsResponse.isSuccess) {
        emit(
          GetIndicatorDetailsErrorState(
            error: indicatorDetailsResponse.errorMessage,
          ),
        );
        return;
      }

      indicatorDetails = indicatorDetailsResponse.response;
      emit(
        GetIndicatorDetailsSuccessState(indicatorDetails: indicatorDetails!),
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        GetIndicatorDetailsErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }
}
