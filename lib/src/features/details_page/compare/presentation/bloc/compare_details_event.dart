part of 'compare_details_bloc.dart';

abstract class CompareDetailsEvent extends DetailsBaseEvent {
  const CompareDetailsEvent();

  @override
  List<Object> get props => [];
}

class GetCompareIndicatorDetailsEvent extends CompareDetailsEvent {
  const GetCompareIndicatorDetailsEvent({required this.indicatorId1, required this.indicatorId2});

  final String indicatorId1;
  final String indicatorId2;

  @override
  List<Object> get props => [indicatorId1, indicatorId2];
}

class LoadIndicatorThemeEvent extends CompareDetailsEvent {
  const LoadIndicatorThemeEvent.official({
    required this.classificationKey,
    required this.domainId,
    required this.theme,
    required this.subTheme,
    this.screenerPayload,
  })  : isOfficial = true;

  const LoadIndicatorThemeEvent.experimental({
    required this.screenerPayload,
  })  : isOfficial = false,
        domainId = null,
        classificationKey = null,
        theme = null,
        subTheme = null;

  final bool isOfficial;
  final String? domainId;
  final String? classificationKey;
  final String? theme;
  final String? subTheme;
  final JSONObject? screenerPayload;

  @override
  List<Object> get props => [
        classificationKey ?? '',
        domainId ?? '',
        theme ?? '',
        subTheme ?? '',
      ];
}

class GetCompareIndicatorListEvent extends CompareDetailsEvent {
  const GetCompareIndicatorListEvent.official({
    required this.domainId,
    required this.classificationId,
    required this.subDomainId,
    required this.subThemeId,
    required this.pageNo,
    this.screenerPayload,
  }) : isOfficial = true;

  const GetCompareIndicatorListEvent.experimental({
    required this.screenerPayload,
    required this.pageNo,
  })  : isOfficial = false,
        domainId = null,
        classificationId = null,
        subDomainId = null,
        subThemeId = null;

  final bool isOfficial;
  final String? domainId;
  final String? classificationId;
  final String? subDomainId;
  final String? subThemeId;
  final int pageNo;
  final JSONObject? screenerPayload;

  @override
  List<Object> get props => [
        classificationId ?? '',
        domainId ?? '',
        subDomainId ?? '',
        subThemeId ?? '',
        screenerPayload.hashCode,
        pageNo,
      ];
}
