import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/check_box_text_row.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/primary_button.dart';
import 'package:scad_mobile/src/features/details_page/base/helper/route_helper.dart';
import 'package:scad_mobile/src/features/details_page/compare/data/models/comparable_indicator.dart';
import 'package:scad_mobile/src/features/details_page/compare/presentation/bloc/compare_details_bloc.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class SelectComparableScreen extends StatefulWidget {
  const SelectComparableScreen({
    required this.indicatorDetails,
    this.screener,
    super.key,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final JSONObject? screener;

  @override
  State<SelectComparableScreen> createState() => _SelectComparableScreenState();
}

class _SelectComparableScreenState extends State<SelectComparableScreen> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  String get domainId => widget.indicatorDetails.domainId ?? '';

  String? get contentClassificationKey => widget.indicatorDetails.contentClassificationKey;

  String? get indicatorId => isOfficial ? widget.indicatorDetails.indicatorId : widget.indicatorDetails.id;

  late String? _classificationId;
  late String? _themeId;
  SubTheme? _subTheme;

  final _indicatorList = <ComparableIndicator>[];
  ValueNotifier<String?> selectedIndicatorId = ValueNotifier(null);

  bool get isOfficial => widget.indicatorDetails.contentClassificationKey == 'official_statistics';

  int _page = 1;
  int _total = 0;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _initialize() {
    final classificationKey = widget.indicatorDetails.contentClassificationKey ?? '';
    final theme = widget.indicatorDetails.theme ?? '';
    final subTheme = widget.indicatorDetails.subtheme ?? '';

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        final event = isOfficial
            ? LoadIndicatorThemeEvent.official(
                classificationKey: classificationKey,
                domainId: domainId,
                theme: theme,
                subTheme: subTheme,
                screenerPayload: widget.screener,
              )
            : LoadIndicatorThemeEvent.experimental(
                screenerPayload: widget.screener,
              );

        context.read<CompareDetailsBloc>().add(event);
      },
    );
  }

  void _loadNextPage([int? pageNo]) {
    final nextPage = pageNo ?? (_page + 1);

    final event = isOfficial
        ? GetCompareIndicatorListEvent.official(
            classificationId: _classificationId,
            domainId: domainId,
            subDomainId: _themeId,
            subThemeId: _subTheme?.id,
            screenerPayload: widget.screener,
            pageNo: nextPage,
          )
        : GetCompareIndicatorListEvent.experimental(
            screenerPayload: widget.screener,
            pageNo: nextPage,
          );

    context.read<CompareDetailsBloc>().add(event);
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FlatAppBar(
              title: LocaleKeys.compareIndicators.tr(),
              bottomPadding: 0,
            ),
            Expanded(
              child: BlocConsumer<CompareDetailsBloc, CompareDetailsState>(
                listenWhen: (_, state) => state is LoadIndicatorThemeBaseState,
                buildWhen: (_, state) =>
                    state is LoadIndicatorThemeBaseState || state is GetCompareIndicatorListSuccessState,
                listener: (context, state) {
                  if (state is LoadOfficialIndicatorSuccessState) {
                    _classificationId = state.classificationId;
                    _themeId = state.themeId;
                    _subTheme = state.subTheme;
                    _total = state.indicatorList.total;
                    _page = state.indicatorList.page;

                    _indicatorList
                      ..clear()
                      ..addAll(state.indicatorList.items);
                  }

                  if (state is LoadExperimentalIndicatorSuccessState) {
                    _total = state.indicatorList.total;
                    _page = state.indicatorList.page;
                    _indicatorList
                      ..clear()
                      ..addAll(state.indicatorList.items);
                  }
                },
                builder: (context, state) {
                  if (state is LoadIndicatorThemeErrorState) {
                    return Center(
                      child: ErrorReloadPlaceholder(
                        error: state.error,
                        onReload: () => _initialize(),
                      ),
                    );
                  }

                  if (state is LoadIndicatorThemeLoadingState ||
                      (state is! LoadIndicatorThemeBaseState && state is! GetCompareIndicatorListBaseState)) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (_indicatorList.isEmpty) {
                    return const Center(
                      child: NoDataPlaceholder(),
                    );
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildDomainAndThemeTitle(),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Flexible(
                              child: _buildIndicatorListTiles(),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 10, 24, 24),
                        child: _buildCompareButton(),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDomainAndThemeTitle() {
    final domainName = widget.indicatorDetails.domain ?? '';

    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 8, 24, 20),
      child: Text(
        '$domainName - ${_subTheme?.name ?? ''}',
        style: TextStyle(
          color: _isLightMode ? AppColors.blackShade1 : AppColors.white,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildIndicatorListTiles() {
    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.all(8),
      itemCount: _indicatorList.length + (_indicatorList.length < _total ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _indicatorList.length) {
          return _buildLoadMore();
        }

        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: ValueListenableBuilder(
            valueListenable: selectedIndicatorId,
            builder: (context, _, __) {
              final indicator = _indicatorList[index];

              return CheckBoxTextRow(
                title: indicator.title ?? '',
                titleColor: _isLightMode ? AppColors.grey : AppColors.greyShade4,
                isDefaultValue: indicatorId == indicator.indicatorId,
                isDisable: !indicator.isComparable,
                isSelected: selectedIndicatorId.value == indicator.indicatorId,
                onChanged: () => selectedIndicatorId.value = indicator.indicatorId ?? '',
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadMore() {
    return BlocConsumer<CompareDetailsBloc, CompareDetailsState>(
      listenWhen: (_, state) => state is GetCompareIndicatorListSuccessState,
      buildWhen: (_, state) => state is GetCompareIndicatorListBaseState,
      listener: (context, state) {
        if (state is GetCompareIndicatorListSuccessState) {
          _page = state.indicatorList.page;
          _total = state.indicatorList.total;
          _indicatorList.addAll(
            state.indicatorList.items,
          );
        }
      },
      builder: (context, state) {
        if (state is GetCompareIndicatorListLoadingState) {
          return const Center(
            child: SizedBox.square(
              dimension: 28,
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (state is GetCompareIndicatorListErrorState) {
          return Center(
            child: ErrorReloadPlaceholder(
              error: state.error,
              onReload: () => _loadNextPage(_page),
            ),
          );
        }

        if (_indicatorList.length >= _total) {
          return const SizedBox.shrink();
        }

        return Center(
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: _loadNextPage,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                LocaleKeys.loadMore.tr(),
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompareButton() {
    final canLoadMore = _indicatorList.length < _total;
    final backgroundColor = _isLightMode ? AppColors.blueShade22 : AppColors.blueLightOld;

    if (!canLoadMore) {
      final hasIndicatorsToCompare = _indicatorList.any(
        (indicator) => indicator.indicatorId != indicatorId && indicator.isComparable,
      );

      if (!hasIndicatorsToCompare) {
        return PrimaryButton(
          text: LocaleKeys.back.tr(),
          onTap: () => Navigator.pop(context),
          backgroundColor: backgroundColor,
          color: AppColors.white,
        );
      }
    }

    return ValueListenableBuilder(
      valueListenable: selectedIndicatorId,
      builder: (context, selectedIndicatorId, child) {
        final disabled = selectedIndicatorId == null;

        return ElevatedButton(
          style: ElevatedButton.styleFrom(
            minimumSize: const Size.fromHeight(43),
            backgroundColor: backgroundColor.withValues(
              alpha: disabled ? 0.5 : 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          onPressed: disabled
              ? null
              : () {
                  DetailsPageRouteHelper.compareIndicatorDetailsPage(
                    context,
                    combinedId: '${indicatorId}_$selectedIndicatorId',
                    indicatorType: widget.indicatorDetails.getIndicatorType(),
                  );
                },
          child: child,
        );
      },
      child: Text(
        LocaleKeys.compare.tr(),
        style: const TextStyle(
          color: AppColors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
