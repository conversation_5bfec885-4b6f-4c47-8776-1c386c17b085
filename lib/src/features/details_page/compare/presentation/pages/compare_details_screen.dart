import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/chart_legend.dart';
import 'package:scad_mobile/src/common/widgets/charts/column_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details_page/base/constants.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/chart_period_filter_mixin.dart';
import 'package:scad_mobile/src/features/details_page/compare/presentation/bloc/compare_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_data_presentation_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_action_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_period_options.dart';
import 'package:scad_mobile/src/features/details_page/widgets/data_table/custom_data_table.dart';
import 'package:scad_mobile/src/features/details_page/widgets/data_table/table_data_grid_source.dart';
import 'package:scad_mobile/src/features/details_page/widgets/domain_and_personalize_controls/domain_and_personalize_controls.dart';
import 'package:scad_mobile/src/features/details_page/widgets/download_as/download_as.dart';
import 'package:scad_mobile/src/features/details_page/widgets/full_screen_chart_view/full_screen_chart_view.dart';
import 'package:scad_mobile/src/features/details_page/widgets/glossary_container.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_type_tag.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/extentions/widget_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';

@RoutePage()
class CompareDetailsScreen extends StatefulWidget {
  const CompareDetailsScreen({
    required this.combinedId,
    super.key,
    this.comparedIndicatorName,
    this.indicatorType,
  });

  final String combinedId; // id format 1234_1235
  final String? comparedIndicatorName;
  final IndicatorType? indicatorType;

  @override
  State<CompareDetailsScreen> createState() => _CompareDetailsScreenState();
}

class _CompareDetailsScreenState extends State<CompareDetailsScreen> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  ChartDataRepresentation _selectedChartRepresentation = ChartDataRepresentation.line;
  final ValueNotifier<ChartDataRepresentation?> _tempSelectedChartRepresentation = ValueNotifier(null);

  String get _indicatorTitle => widget.comparedIndicatorName ?? LocaleKeys.compareIndicatorsResult.tr();

  IndicatorDetailsResponse? get _indicatorDetails => context.read<CompareDetailsBloc>().indicatorDetails;

  VisualizationsMeta? get _visualizationsMeta => _indicatorDetails?.indicatorVisualizations?.visualizationsMeta
      ?.where((e) => e.id == _indicatorDetails?.indicatorVisualizations?.visualizationDefault)
      .firstOrNull;

  String? get yAxisLabel => _visualizationsMeta?.seriesMeta!.firstOrNull?.label?.toString();

  String? get yAxisLabelRight => _visualizationsMeta?.seriesMeta!.lastOrNull?.label?.toString();

  final _scrollController = ScrollController();
  final _screenshotController = ScreenshotController();

  final _seriesList = <SeriesData>[];

  List<TableDataGridSource> tableList = [];
  final ValueNotifier<int> tableIndex = ValueNotifier(0);

  final _availablePeriodOptions = <ChartPeriodOption>[];
  final _selectedPeriodOption = ValueNotifier<ChartPeriodOption?>(null);

  @override
  void initState() {
    super.initState();

    context.read<CompareDetailsBloc>().resetBlocVariables();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _loadIndicatorDetails();
    });
  }

  void _loadIndicatorDetails() {
    final arr = widget.combinedId.split('_');
    final indicatorId1 = arr.elementAtOrNull(0) ?? '';
    final indicatorId2 = arr.elementAtOrNull(1) ?? '';

    context.read<CompareDetailsBloc>().add(
          GetCompareIndicatorDetailsEvent(
            indicatorId1: indicatorId1,
            indicatorId2: indicatorId2,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: _indicatorTitle,
              bottomPadding: 0,
              isDetailsPage: true,
              scrollController: _scrollController,
            ),
            Expanded(
              child: BlocConsumer<CompareDetailsBloc, CompareDetailsState>(
                buildWhen: (_, state) => state is GetCompareIndicatorDetailsBaseState,
                listenWhen: (_, state) => state is GetCompareIndicatorDetailsBaseState,
                listener: (context, state) {
                  if (state is! GetCompareIndicatorDetailsSuccessState) return;

                  _seriesList.clear();
                  for (int i = 0; i < (_visualizationsMeta?.seriesMeta ?? []).length; i++) {
                    _seriesList.add(
                      (_visualizationsMeta?.seriesMeta![i].data ?? []).toList(),
                    );
                  }

                  final availableOptions = _indicatorDetails?.indicatorFilters?.firstOrNull?.options
                      ?.map(
                        (e) => ChartPeriodOption.fromOption(e),
                      )
                      .toList();

                  if (_seriesList.any((e) => e.length > kRecentLength)) {
                    availableOptions?.add(
                      ChartPeriodOption.recent(),
                    );
                  }

                  _availablePeriodOptions.addAll(availableOptions ?? []);
                  _selectedPeriodOption.value = _availablePeriodOptions.lastOrNull;
                },
                builder: (context, state) {
                  if (state is GetCompareIndicatorDetailsErrorState) {
                    return Center(
                      child: ErrorReloadPlaceholder(
                        error: state.error,
                        onReload: _loadIndicatorDetails,
                      ),
                    );
                  }

                  if (state is GetCompareIndicatorDetailsLoadingState && _indicatorDetails == null) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  return _buildBody(state);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(CompareDetailsState state) {
    return SingleChildScrollView(
      controller: _scrollController,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: MediaQuery.sizeOf(context).height,
        ),
        child: IgnorePointer(
          ignoring: state is GetCompareIndicatorDetailsLoadingState,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_indicatorDetails != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: DomainAndPersonalizeControls(
                    indicatorDetails: _indicatorDetails!,
                    contentType: _indicatorDetails?.type ?? '',
                    hideNotification: true,
                  ),
                ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 12),
                    _buildChartHeader(),
                    const SizedBox(height: 24),
                    Screenshot(
                      key: Key('compare.indicator.details.chart.${widget.combinedId}'),
                      controller: _screenshotController,
                      child: Container(
                        constraints: const BoxConstraints(minHeight: 200),
                        color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: _buildChart(),
                      ),
                    ),
                    _buildPeriodFilter(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                    const SizedBox(height: 12),
                    _buildDownloadAs(),
                    const SizedBox(height: 32),
                    const GlossaryContainer(),
                    const SizedBox(height: 48),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          const IndicatorTypeTag.compare(),
          const Spacer(),
          FullScreenExpandButton(
            onTap: () {
              FullScreenChartView.show(
                context: context,
                title: _indicatorTitle,
                titleSuffixBuilder: _buildTableNavigator,
                chartBuilder: (context) => _buildChart(isFullScreen: true),
              );
            },
          ),
        ],
      ),
    );
  }

  SeriesDataList _filterPeriod(SeriesDataList series, ChartPeriodOption? option) {
    if (option == null) {
      return series;
    }

    if (option.id?.toLowerCase() == 'all') return series;

    if (option.id?.toLowerCase() == LocaleKeys.recent) {
      return series
          .map(
            (e) => e.reversed.take(12).toList().reversed.toList(),
          )
          .toList();
    }

    final startDate = DateTime.tryParse(
          _visualizationsMeta?.seriesMeta?.firstOrNull?.xMax ?? '',
        ) ??
        DateTime.now();

    final isPeriodYear = option.unit?.toLowerCase() == 'years';
    final value = option.value ?? 0;
    final endDate = isPeriodYear
        ? DateTime(startDate.year - value, startDate.month, startDate.day)
        : DateTime(startDate.year, startDate.month - value, startDate.day);

    final periodFilteredSeries = <SeriesData>[];
    for (final element in series) {
      final filteredData = element.where((entry) {
        final obsDate = DateTime.parse(entry['OBS_DT'] as String);
        return obsDate.compareTo(endDate) >= 0 && obsDate.compareTo(startDate) <= 0;
      }).toList();

      periodFilteredSeries.add(filteredData);
    }

    return periodFilteredSeries;
  }

  Widget _buildPeriodFilter() {
    if (_availablePeriodOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final hasSeries = _seriesList.any((e) => e.isNotEmpty);
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 8, right: 8),
      child: AnimatedOpacity(
        opacity: hasSeries ? 1 : 0.3,
        duration: const Duration(milliseconds: 300),
        child: IgnorePointer(
          ignoring: !hasSeries,
          child: ChartPeriodSwitcher(
            initialSelectedIndex: _availablePeriodOptions.indexWhere(
              (e) => _selectedPeriodOption.value?.id == e.id,
            ),
            options: _availablePeriodOptions,
            onChanged: (option) => _selectedPeriodOption.value = option,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final hasSeries = _seriesList.isNotEmpty && (_seriesList.any((e) => e.isNotEmpty));

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Expanded(
              child: ChartActionButton.frequency(
                enabled: false,
              ),
            ),
            Expanded(
              child: ChartActionButton.presentation(
                enabled: hasSeries,
                onPressed: () async {
                  _tempSelectedChartRepresentation.value = _selectedChartRepresentation;

                  await SelectDataPresentationBottomSheet.show(
                    context,
                    options: [
                      ChartDataRepresentation.line,
                      ChartDataRepresentation.bar,
                      ChartDataRepresentation.table,
                    ],
                    initialRepresentation: _selectedChartRepresentation,
                    onPreview: (value) => _tempSelectedChartRepresentation.value = value,
                    onDone: (value) => _selectedChartRepresentation = value,
                  );
                  _tempSelectedChartRepresentation.value = null;
                },
              ),
            ),
            const Expanded(
              child: ChartActionButton.compare(
                enabled: false,
              ),
            ),
            const Expanded(
              child: ChartActionButton.compute(
                enabled: false,
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildDownloadAs() {
    return ValueListenableBuilder(
      valueListenable: _tempSelectedChartRepresentation,
      builder: (context, value, child) {
        return DownloadAsV2.restricted(
          key: Key('compare-${_selectedChartRepresentation.index}'),
        );
      },
    );
  }

  Widget _buildChart({
    bool isFullScreen = false,
    bool isDownload = false,
    bool? isLightMode,
  }) {
    final hasSeries = _seriesList.any((e) => e.isNotEmpty);
    if (!hasSeries) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder(
      valueListenable: _selectedPeriodOption,
      builder: (context, selectedPeriodOption, child) {
        final series = _seriesList;
        final periodFilteredSeries = _filterPeriod(
          series,
          selectedPeriodOption,
        );

        return ValueListenableBuilder(
          valueListenable: _tempSelectedChartRepresentation,
          builder: (context, _, __) {
            final frequency = IndicatorDateSetting.setFrequancy(
              l: periodFilteredSeries,
              indicatorDetails: IndicatorDetailsResponseHelper(_indicatorDetails!),
            )['selectedFrequencyForFilter'] as String;

            final chartRepresentation = _tempSelectedChartRepresentation.value ?? _selectedChartRepresentation;
            if (chartRepresentation == ChartDataRepresentation.line) {
              return Column(
                children: [
                  SplineChart(
                    showMarker: isDownload,
                    frequency: frequency,
                    isLightMode: isLightMode,
                    chartDataList: periodFilteredSeries
                        .map(
                          (e) => e
                              .map(
                                (e1) => SplineChartData(
                                  e1['OBS_DT'].toString(),
                                  num.parse('${e1['VALUE'] ?? '0'}'),
                                ),
                              )
                              .toList(),
                        )
                        .toList(),
                    isCompareActive: true,
                    yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
                    yAxisLabelRight: yAxisLabelRight?.limitLength(splitLines: 40),
                  ).wrapInExpand(isFullScreen),
                  const SizedBox(height: 8),
                  _buildLegends(isLightMode: isLightMode),
                ],
              );
            } else if (chartRepresentation == ChartDataRepresentation.bar) {
              return Column(
                children: [
                  ColumnChart(
                    showMarker: isDownload,
                    frequency: frequency,
                    isLightMode: isLightMode,
                    chartDataList: periodFilteredSeries
                        .map(
                          (e) => e
                              .map(
                                (e1) => ColumnChartData(
                                  e1['OBS_DT'].toString(),
                                  double.parse('${e1['VALUE'] ?? '0'}'),
                                  null,
                                  null,
                                ),
                              )
                              .toList(),
                        )
                        .toList(),
                    isCompareActive: true,
                    yAxisLabel: yAxisLabel?.limitLength(splitLines: 40),
                    yAxisLabelRight: yAxisLabelRight?.limitLength(splitLines: 40),
                  ).wrapInExpand(isFullScreen),
                  const SizedBox(height: 8),
                  _buildLegends(isLightMode: isLightMode),
                ],
              );
            } else if (chartRepresentation == ChartDataRepresentation.table) {
              if (isFullScreen) {
                return _buildTableView(
                  periodFilteredSeries,
                  showFullScreen: isFullScreen,
                );
              } else {
                return Column(
                  children: [
                    _buildTableView(
                      periodFilteredSeries,
                      showFullScreen: isFullScreen,
                    ),
                    _buildTableNavigator(context),
                  ],
                );
              }
            }

            return const SizedBox();
          },
        );
      },
    );
  }

  Widget _buildTableView(SeriesDataList series, {bool showFullScreen = false}) {
    tableList = [];
    final List<List<Map<String, dynamic>>> seriesDataList = List.generate(
      series.length,
      (outerIndex) => List.generate(
        series[outerIndex].length,
        (innerIndex) => jsonDecode(
          jsonEncode(
            series[outerIndex][innerIndex],
          ),
        ) as Map<String, dynamic>,
      ),
    );

    // todo add number formatting for decimal
    // for (int i = 0; i < seriesDataList.length; i++) {
    //   for (int j = 0; j < seriesDataList[i].length; j++) {
    //     for (int k = 0; k < seriesDataList[i][j].entries.length; k++) {
    //       final MapEntry<String, dynamic> m = seriesDataList[i][j].entries.toList()[k];
    //
    //       if (double.tryParse('${m.value}') != null && m.key != 'YEAR') {
    //         seriesDataList[i][j][m.key] = double.parse(m.value.toString()).toStringAsFixed(2);
    //       }
    //     }
    //   }
    // }

    // seriesDataList.removeWhere((element) => element.isEmpty);

    // todo check indicator with tablefields
    final List<TableFields> tableFields = [];
    // for (final TableFields element in _selectedVisualization?.tableFields ?? []) {
    //   if (seriesDataList.firstOrNull?.firstOrNull?.containsKey(element.path) ?? false) {
    //     tableFields.add(element);
    //   }
    // }

    // for (final collection in seriesDataList) {
    //   for (final element in collection) {
    //     element['OBS_DT'] = IndicatorDateSetting.setupNameAll(
    //         // todo set frequency if needed
    //         'Yearly', //selectedFrequencyForFilter ?? '',
    //         element['OBS_DT'] as String? ?? '');
    //   }
    // }

    seriesDataList.toList().forEach((series) {
      // keys = tableFields.isNotEmpty ? tableFields.map((e) => e.label).toList() : keys;
      final List<String> keys = (series.firstOrNull?.keys ?? []).toList();

      tableList.add(
        TableDataGridSource(
          tableData: series,
          columnNames: tableFields.isNotEmpty ? tableFields.map((e) => e.path ?? '').toList() : keys,
        ),
      );
    });

    return ValueListenableBuilder(
      valueListenable: tableIndex,
      builder: (context, _, __) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Container(
            decoration: const BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: AppColors.greyShade12,
                  blurRadius: 50,
                  offset: Offset(0, 35),
                  spreadRadius: -23,
                ),
              ],
            ),
            height: 300,
            width: double.maxFinite,
            child: CustomDataTable(
              key: Key('compare.tableview.${_indicatorDetails?.id}.${tableIndex.value}.$showFullScreen'),
              headerCells: tableList[tableIndex.value].columnNames,
              rowsCells: tableList[tableIndex.value].rows,
              isLightMode: _isLightMode,
              isLandscape: showFullScreen,
            ),
          ),
        );
      },
    );
  }

  Widget _buildTableNavigator(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: tableIndex,
      builder: (context, _, __) {
        if ((_selectedChartRepresentation == ChartDataRepresentation.table ||
                _tempSelectedChartRepresentation.value == ChartDataRepresentation.table) &&
            tableList.length > 1) {
          return Padding(
            padding: const EdgeInsets.all(4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: tableIndex.value <= 0
                        ? null
                        : () {
                            tableIndex.value = tableIndex.value - 1;
                          },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(14),
                      child: RotatedBox(
                        quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                        child: SvgPicture.asset(
                          AppImages.icArrowLeft,
                          colorFilter: ColorFilter.mode(
                            tableIndex.value <= 0
                                ? AppColors.greyShade13
                                : _isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.blue,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 12,
                ),
                Text(
                  '${tableIndex.value + 1} / ${tableList.length}',
                ),
                const SizedBox(
                  width: 12,
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: tableIndex.value >= tableList.length - 1
                        ? null
                        : () {
                            tableIndex.value = tableIndex.value + 1;
                          },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.all(14),
                      child: RotatedBox(
                        quarterTurns: DeviceType.isDirectionRTL(context) ? 0 : 2,
                        child: SvgPicture.asset(
                          AppImages.icArrowLeft,
                          colorFilter: ColorFilter.mode(
                            tableIndex.value >= tableList.length - 1
                                ? AppColors.greyShade13
                                : _isLightMode
                                    ? AppColors.blueLight
                                    : AppColors.blue,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return const SizedBox();
      },
    );
  }

  Widget _buildLegends({bool? isLightMode}) {
    if (_selectedChartRepresentation == ChartDataRepresentation.table) {
      return const SizedBox();
    }

    final List<String> legendList = _visualizationsMeta?.seriesTitles?.values.map((e) => e.toString()).toList() ?? [];

    final List<Color> colorSet = (isLightMode ?? _isLightMode) ? AppColors.chartColorSet : AppColors.chartColorSetDark;

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: List.generate(
        legendList.length,
        (index) => ChartLegend(
          label: legendList[index],
          color: colorSet[index % colorSet.length],
          isLightMode: isLightMode,
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }
}
