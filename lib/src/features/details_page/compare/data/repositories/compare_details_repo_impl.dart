import 'dart:async';

import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details_page/compare/data/data_sources/compare_details_end_points.dart';
import 'package:scad_mobile/src/features/details_page/compare/domain/repositories/compare_details_repository.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';

class CompareDetailsRepoImpl extends CompareDetailsRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> compareIndicators({
    required String indicatorId1,
    required String indicatorId2,
  }) async {
    final endpoint = CompareDetailsEndPoints.compareEndPoint;
    final body = {
      'nodes': [
        {'indicatorId': indicatorId1},
        {'indicatorId': indicatorId2},
      ],
    };

    final cacheKey = getCacheKey(endpoint, payload: body);
    return fetchWithCache<IndicatorDetailsResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: body,
      ),
      parseResult: (json) => IndicatorDetailsResponse.fromJson(json),
    );
  }
}
