part of 'scenario_forecast_details_bloc.dart';

abstract class ScenarioForecastDetailsState extends DetailsBaseState {
  const ScenarioForecastDetailsState({required this.isDriverChange});

  final bool isDriverChange;

  @override
  List<Object> get props => [];
}

class ScenarioDiverChangeDriverLoadingState extends ScenarioForecastDetailsState {
  const ScenarioDiverChangeDriverLoadingState({required super.isDriverChange});
}

class ScenarioDiverChangeDriverSuccessState extends ScenarioForecastDetailsState {
  const ScenarioDiverChangeDriverSuccessState({
    required super.isDriverChange,
    required this.indicatorDetails,
  });

  final IndicatorDetailsResponse indicatorDetails;

  @override
  List<Object> get props => [indicatorDetails];
}

class ScenarioDiverChangeDriverErrorState extends ScenarioForecastDetailsState {
  const ScenarioDiverChangeDriverErrorState({required super.isDriverChange, required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class UpdatedDriverApplyState extends ScenarioForecastDetailsState {
  const UpdatedDriverApplyState({
    required this.indicatorDetails,
    required this.payload,
    required this.scadProjectionState,
    super.isDriverChange = false,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final Map<String, String> payload;
  final bool scadProjectionState;

  @override
  List<Object> get props => [indicatorDetails, payload, scadProjectionState];
}

class ApplyForecastFilterSuccessState extends ScenarioForecastDetailsState {
  ApplyForecastFilterSuccessState() : super(isDriverChange: false) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}
