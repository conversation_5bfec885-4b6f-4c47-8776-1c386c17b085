part of 'scenario_forecast_details_bloc.dart';

abstract class ScenarioForecastDetailsEvent extends DetailsBaseEvent {
  const ScenarioForecastDetailsEvent();

  @override
  List<Object> get props => [];
}

class ScenarioDriverChangeEvent extends ScenarioForecastDetailsEvent {
  const ScenarioDriverChangeEvent({
    required this.contentType,
    required this.id,
    required this.isDriverChange,
    required this.payload,
  });

  final bool isDriverChange;
  final String id;
  final String contentType;
  final Map<String, String> payload;

  @override
  List<Object> get props => [isDriverChange, contentType, id, payload];
}

class UpdatedDriverApplyEvent extends ScenarioForecastDetailsEvent {
  const UpdatedDriverApplyEvent({
    required this.payload,
    required this.indicatorDetails,
    required this.scadProjectionState,
  });

  final Map<String, String> payload;
  final IndicatorDetailsResponse indicatorDetails;
  final bool scadProjectionState;

  @override
  List<Object> get props => [indicatorDetails, payload, scadProjectionState];
}

class ApplyForecastFilterEvent extends ScenarioForecastDetailsEvent {
  const ApplyForecastFilterEvent({required this.payload});

  final SelectedFilterMap payload;

  @override
  List<Object> get props => [payload];
}
