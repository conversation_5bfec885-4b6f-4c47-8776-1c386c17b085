import 'dart:async';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/base/constants.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/create_sme_thread_mixin.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/indicator_filter_adapter_mixin.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'scenario_forecast_details_event.dart';
part 'scenario_forecast_details_state.dart';

class ScenarioForecastDetailsBloc extends DetailsBaseBloc with CreateSMEThreadMixin, IndicatorFilterAdapterMixin {
  ScenarioForecastDetailsBloc() {
    onCreateSMEThreadListener();

    on<ScenarioDriverChangeEvent>(_onScenarioDriverChangeEvent);
    on<UpdatedDriverApplyEvent>(_onUpdatedDriverApplyEvent);
    on<ApplyForecastFilterEvent>(_onApplyForecastFilterEvent);
  }

  FilterPanel? _filterPanel;

  @override
  FilterPanel? get filterPanel => _filterPanel;

  void _initializeFilter(IndicatorDetailsResponse indicatorDetails) {
    final filterPanel = indicatorDetails.filterPanel;
    if (filterPanel == null || filterPanel is! FilterPanel) {
      selectedFilterMap.clear();
      return;
    }

    _filterPanel = filterPanel;
    initializeDefaultFilter();
  }

  MapEntry<String, List<String>> getMultiSelectedFilter() {
    final selectedFilterWithoutObsDt = selectedFilterMap.entries.where((e) {
      if (e.key == kObsDt) return false;
      final property = filterPanel?.properties?.singleWhere((filter) => filter.path == e.key);
      return property?.type != 'radio';
    });

    final multiSelectFilter = selectedFilterWithoutObsDt.firstWhere(
      (e) => e.value.length > 1,
      orElse: () => selectedFilterWithoutObsDt.firstWhere(
        (e) {
          final property = filterPanel?.properties?.singleWhere((filter) => filter.path == e.key);
          return e.value.firstOrNull != property?.defaultVal;
        },
        orElse: () => selectedFilterWithoutObsDt.first,
      ),
    );
    return multiSelectFilter;
  }

  FutureOr<void> _onScenarioDriverChangeEvent(
    ScenarioDriverChangeEvent event,
    Emitter<DetailsBaseState> emit,
  ) async {
    _filterPanel = null;

    try {
      emit(
        ScenarioDiverChangeDriverLoadingState(isDriverChange: event.isDriverChange),
      );

      final RepoResponse<IndicatorDetailsResponse> indicatorDetailsResponse =
          await servicelocator<IndicatorCardRepository>().indicatorDetails(
        id: event.id,
        contentType: event.contentType,
        payload: event.payload,
      );

      if (indicatorDetailsResponse.isSuccess) {
        final IndicatorDetailsResponse indicatorDetails = indicatorDetailsResponse.response!;
        _initializeFilter(indicatorDetails);

        emit(
          ScenarioDiverChangeDriverSuccessState(
            isDriverChange: event.isDriverChange,
            indicatorDetails: indicatorDetails,
          ),
        );
      } else {
        emit(
          ScenarioDiverChangeDriverErrorState(
            isDriverChange: event.isDriverChange,
            error: indicatorDetailsResponse.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        ScenarioDiverChangeDriverErrorState(
          isDriverChange: event.isDriverChange,
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  FutureOr<void> _onUpdatedDriverApplyEvent(UpdatedDriverApplyEvent event, Emitter<DetailsBaseState> emit) {
    emit(
      UpdatedDriverApplyState(
        indicatorDetails: event.indicatorDetails,
        payload: event.payload,
        scadProjectionState: event.scadProjectionState,
      ),
    );
  }

  void _onApplyForecastFilterEvent(
    ApplyForecastFilterEvent event,
    Emitter<DetailsBaseState> emit,
  ) {
    selectedFilterMap
      ..clear()
      ..addAll(event.payload);

    emit(ApplyForecastFilterSuccessState());
  }
}
