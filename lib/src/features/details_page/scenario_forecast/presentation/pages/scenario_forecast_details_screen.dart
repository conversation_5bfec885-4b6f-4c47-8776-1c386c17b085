import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/bloc/scenario_forecast_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/widgets/expandable_indicator_card.dart';
import 'package:scad_mobile/src/features/details_page/widgets/change_driver_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/domain_and_personalize_controls/domain_and_personalize_controls.dart';
import 'package:scad_mobile/src/features/details_page/widgets/glossary_container.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ScenarioForecastDetailsScreen extends StatefulWidget {
  const ScenarioForecastDetailsScreen({
    required this.id,
    required this.contentType,
    required this.title,
    super.key,
  });

  final String id;
  final String contentType;
  final String title;

  @override
  State<ScenarioForecastDetailsScreen> createState() => _ScenarioForecastDetailsScreenState();
}

class _ScenarioForecastDetailsScreenState extends State<ScenarioForecastDetailsScreen>
    with SingleTickerProviderStateMixin {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  final ScrollController _scrollController = ScrollController();

  late TabController _tabController;
  final PageController _pageController = PageController();

  IndicatorDetailsResponse? _indicatorDetails;
  IndicatorDetailsResponse? _updatedDriverIndicatorDetails;

  IndicatorDetailsResponse? get _currentIndicatorDetails =>
      _isScadProjectionOn ? _indicatorDetails : _updatedDriverIndicatorDetails ?? _indicatorDetails;

  int _index = 0;

  bool _isScadProjectionOn = true;


  Map<String, String> _updatedDriverPayload = {};

  List<VisualizationsMeta?>? get _visualizationsMeta =>
      _currentIndicatorDetails?.indicatorVisualizations?.visualizationsMeta;

  bool get _isWhatIfIndicator =>
      _currentIndicatorDetails?.type == 'Internal' && _currentIndicatorDetails?.multiDrivers == true;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _loadIndicatorDetails();
    });
  }

  void _loadIndicatorDetails() {
    context.read<ScenarioForecastDetailsBloc>().add(
          ScenarioDriverChangeEvent(
            isDriverChange: false,
            id: widget.id,
            contentType: widget.contentType,
            payload: _updatedDriverPayload,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: widget.title,
              bottomPadding: 0,
              isDetailsPage: true,
              scrollController: _scrollController,
            ),
            Expanded(
              child: BlocConsumer<ScenarioForecastDetailsBloc, DetailsBaseState>(
                listenWhen: (_, state) => state is ScenarioForecastDetailsState && !state.isDriverChange,
                buildWhen: (_, state) => state is ScenarioForecastDetailsState && !state.isDriverChange,
                listener: (context, state) async {
                  if (state is UpdatedDriverApplyState) {
                    _updatedDriverIndicatorDetails = state.indicatorDetails;
                    _updatedDriverPayload = state.payload;
                    _isScadProjectionOn = state.scadProjectionState;

                    // _checkAndSetTreemapChartType();
                  } else if (state is ScenarioDiverChangeDriverSuccessState && !state.isDriverChange) {
                    _indicatorDetails = state.indicatorDetails;
                    // _checkAndSetTreemapChartType();

                    if (_isWhatIfIndicator) {
                      _tabController = TabController(
                        length:
                            (_currentIndicatorDetails?.indicatorVisualizations?.visualizationsMeta ?? []).length + 1,
                        vsync: this,
                      );
                    }
                    // _setCurrentChartData(_index);
                    // _setFrequencyList();
                  }
                },
                builder: (context, state) {
                  if (state is ScenarioDiverChangeDriverLoadingState && !state.isDriverChange) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (state is ScenarioDiverChangeDriverErrorState && !state.isDriverChange) {
                    return Center(
                      child: ErrorReloadPlaceholder(
                        error: state.error,
                        onReload: _loadIndicatorDetails,
                      ),
                    );
                  }

                  if (_currentIndicatorDetails == null || state is! ScenarioForecastDetailsState) {
                    return const SizedBox();
                  }

                  return Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: _isWhatIfIndicator ? _buildBodyContentWhatIf(state) : _buildBodyContent(state),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBodyContentWhatIf(ScenarioForecastDetailsState state) {
    final visualizationsMeta = _visualizationsMeta ?? [];

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        AppSlidingTab(
          tabController: _tabController,
          pageController: _pageController,
          onTabChange: (index) {
            _index = index;
            _pageController.animateToPage(index, duration: const Duration(milliseconds: 200), curve: Curves.linear);
          },
          tabs: [
            AppSlidingTabItem(label: LocaleKeys.all.tr()),
            ...visualizationsMeta.map(
              (e) => AppSlidingTabItem(label: (e?.seriesMeta?.firstOrNull?.label as String?) ?? ''),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
          child: Align(alignment: Alignment.centerLeft, child: _buildChangeDriverButton()),
        ),
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (i) => _tabController.animateTo(i),
            children: [
              ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: visualizationsMeta.length,
                itemBuilder: (context, index) {
                  return ExpandableIndicatorCard(
                    id: widget.id,
                    contentType: widget.contentType,
                    indicatorType: _currentIndicatorDetails!.getIndicatorType(),
                    title: visualizationsMeta[index]?.seriesMeta?.firstOrNull?.label?.toString() ?? '',
                    index: index,
                    currentIndicatorDetails: _currentIndicatorDetails!,
                    isScadProjectionOn: _isScadProjectionOn,
                    changeDriverPayload: _updatedDriverPayload,
                  );
                },
              ),
              ...List.generate(visualizationsMeta.length, (index) {
                return SingleChildScrollView(
                  child: ExpandableIndicatorCard(
                    id: widget.id,
                    contentType: widget.contentType,
                    indicatorType: _currentIndicatorDetails!.getIndicatorType(),
                    title: visualizationsMeta[index]?.seriesMeta?.firstOrNull?.label?.toString() ?? '',
                    index: index,
                    currentIndicatorDetails: _currentIndicatorDetails!,
                    isScadProjectionOn: _isScadProjectionOn,
                    changeDriverPayload: _updatedDriverPayload,
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBodyContent(ScenarioForecastDetailsState state) {
    return SingleChildScrollView(
      controller: _scrollController,
      child: ConstrainedBox(
        constraints: BoxConstraints(minHeight: MediaQuery.sizeOf(context).height),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: DomainAndPersonalizeControls(
                indicatorDetails: _indicatorDetails!,
                contentType: widget.contentType,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 18),
              decoration: BoxDecoration(
                color: _isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ExpandableIndicatorCard(
                    id: widget.id,
                    contentType: widget.contentType,
                    title: widget.title,
                    index: _index,
                    indicatorType: _currentIndicatorDetails!.getIndicatorType(),
                    isScadProjectionOn: _isScadProjectionOn,
                    changeDriverPayload: _updatedDriverPayload,
                    currentIndicatorDetails: _currentIndicatorDetails!,
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: GlossaryContainer(),
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChangeDriverButton() {
    final drivers = _currentIndicatorDetails?.indicatorDrivers ?? [];
    if (drivers.isEmpty) {
      return const SizedBox();
    }

    return ChangeDriverButton(
      alignment: Alignment.centerRight,
      onPressed: () {
        AutoRouter.of(context).push(
          ScenarioDriverModifierScreenRoute(
            contentType: widget.contentType,
            indicatorDetails: _currentIndicatorDetails!,
            payload: _updatedDriverPayload,
            isScadProjectionOn: _isScadProjectionOn,
            index: _index,
          ),
        );
      },
    );
  }
}
