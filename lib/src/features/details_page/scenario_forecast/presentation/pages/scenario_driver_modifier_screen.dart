import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/charts/spline_chart.dart';
import 'package:scad_mobile/src/common/widgets/charts/treemap_chart.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/bloc/scenario_forecast_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/widgets/driver_slider/driver_slider_widget.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/indicator_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

const _kMinValue = 0.0;
const _kMaxValue = 100.0;

@RoutePage()
class ScenarioDriverModifierScreen extends StatefulWidget {
  const ScenarioDriverModifierScreen({
    required this.indicatorDetails,
    required this.payload,
    required this.contentType,
    required this.isScadProjectionOn,
    this.index = 0,
    super.key,
  });

  final String contentType;
  final int index;
  final IndicatorDetailsResponse indicatorDetails;
  final bool isScadProjectionOn;
  final Map<String, String> payload;

  @override
  State<ScenarioDriverModifierScreen> createState() => _ScenarioDriverModifierScreenState();
}

class _ScenarioDriverModifierScreenState extends State<ScenarioDriverModifierScreen> {
  final _isLightMode = HiveUtilsSettings.isLightMode;
  final _isArabic = HiveUtilsSettings.isLanguageArabic;

  final _scrollController = ScrollController();

  late IndicatorDetailsResponse indicatorDetails = widget.indicatorDetails;

  Map<String, String> get _payload => widget.payload;

  late final ValueNotifier<bool> _scadProjectionState = ValueNotifier(widget.isScadProjectionOn);
  final ValueNotifier<int> _selectedTabIndex = ValueNotifier(0); //selectedDomainTabIndex
  final ValueNotifier<List<int>> _selectedDrivers = ValueNotifier([]);

  List<IndicatorDriver> get _indicatorDrivers => indicatorDetails.indicatorDrivers ?? [];

  @override
  void initState() {
    super.initState();

    _selectedTabIndex.value = (widget.index == 0 || widget.index == 1) ? 0 : widget.index - 1;

    if (!_indicatorDrivers.any((e) => e.viewOnly == true)) {
      _scadProjectionState.value = false;
    }

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _initDriverValues();
    });
  }

  void _initDriverValues() {
    if (_scadProjectionState.value && _selectedDrivers.value.isNotEmpty) return;

    _selectedDrivers.value = [];
    for (int i = 0; i < _indicatorDrivers.length; i++) {
      _selectedDrivers.value.add((_indicatorDrivers[i].options ?? []).indexWhere((e) => e.isSelected!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        centerTitle: true,
        title: Text(
          indicatorDetails.title,
          style: AppTextStyles.s18w5cBlackShade.copyWith(color: _isLightMode ? null : AppColors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close_rounded),
          onPressed: () {
            context.back();
          },
        ),
      ),
      body: BlocConsumer<ScenarioForecastDetailsBloc, DetailsBaseState>(
        listenWhen: (_, state) => state is ScenarioForecastDetailsState && state.isDriverChange,
        buildWhen: (_, state) => state is ScenarioForecastDetailsState && state.isDriverChange,
        listener: (context, state) {
          if (state is ScenarioDiverChangeDriverSuccessState) {
            indicatorDetails = state.indicatorDetails;
            _initDriverValues();
          }
        },
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildLinearProgressIndicator(state),
              _buildWhatIfTabs(),
              const SizedBox(height: 10),
              _buildChartWidget(),
              const SizedBox(height: 10),
              _buildDriverControls(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLinearProgressIndicator(DetailsBaseState state) {
    return state is ScenarioDiverChangeDriverLoadingState
        ? LinearProgressIndicator(
            color: AppColors.blue,
            backgroundColor: AppColors.blue.withValues(alpha: 0.1),
          )
        : const SizedBox(height: 4);
  }

  Widget _buildWhatIfTabs() {
    return indicatorDetails.type == 'Internal' && indicatorDetails.multiDrivers == true
        ? AppSlidingTab(
            initialTabIndex: _selectedTabIndex.value,
            onTabChange: (int index) {
              _selectedTabIndex.value = index;
            },
            tabs: (indicatorDetails.indicatorVisualizations!.visualizationsMeta!)
                .map((e) => AppSlidingTabItem(label: e.vizLabel ?? ''))
                .toList(),
          )
        : const SizedBox();
  }

  Widget _buildChartWidget() {
    return Container(
      height: 200,
      padding: const EdgeInsets.symmetric(horizontal: 14),
      child: ValueListenableBuilder(
        valueListenable: _selectedTabIndex,
        builder: (context, selectedTabIndex, child) {
          final chartType = indicatorDetails.indicatorVisualizations?.visualizationsMeta != null
              ? indicatorDetails.getFilteredVisualizationMetaList().first.type ?? ''
              : '';

          return chartType == 'tree-map-with-change-chart' ? _treeMapChartRepresentation() : _splineChart();
        },
      ),
    );
  }

  Widget _buildDriverControls() {
    return Expanded(
      child: Container(
        width: double.infinity,
        margin: MediaQuery.orientationOf(context) == Orientation.portrait
            ? EdgeInsets.zero
            : const EdgeInsets.only(right: 12, bottom: 12),
        decoration: BoxDecoration(
          color: _isLightMode ? AppColors.white : AppColors.blueShade32,
          borderRadius: MediaQuery.orientationOf(context) == Orientation.portrait
              ? const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                )
              : BorderRadius.circular(20),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.changeDrivers.tr(),
                    style: AppTextStyles.s16w5cBlackShade1.copyWith(
                      color: !_isLightMode ? AppColors.white : null,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    LocaleKeys.modifyDrivers.tr(),
                    style: AppTextStyles.s12w4cGreyShade4.copyWith(
                      color: _isLightMode ? AppColors.grey : null,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Scrollbar(
                controller: _scrollController,
                thumbVisibility: true,
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  controller: _scrollController,
                  shrinkWrap: true,
                  itemCount: _indicatorDrivers.length,
                  itemBuilder: (context, index) {
                    final IndicatorDriver item = _indicatorDrivers[index];
                    final List<DriverOptions> options = item.options ?? [];
                    final double interval = _kMaxValue / (options.length - 1);

                    int getOptionIndex(double value) {
                      return (value / interval).round();
                    }

                    double getValue() {
                      double selectedValue = 0;
                      for (int i = 0; i < options.length; i++) {
                        if (options[i].isSelected ?? false) {
                          selectedValue = interval * i;
                        }
                      }
                      return selectedValue;
                    }

                    final List<DriverOptions> transformedOptions = [];

                    if (_isArabic) {
                      for (final DriverOptions option in options) {
                        String transformedOption = option.label!.replaceAll('-', '').replaceAll('+', '');
                        if (option.label!.startsWith('-')) {
                          transformedOption += '-';
                        } else if (option.label!.startsWith('+')) {
                          transformedOption += '+';
                        }
                        transformedOptions.add(
                          DriverOptions(
                            label: transformedOption,
                            value: option.value,
                            isSelected: option.isSelected,
                          ),
                        );
                      }
                    }

                    return ValueListenableBuilder(
                      valueListenable: _scadProjectionState,
                      builder: (context, _, __) {
                        if (item.viewOnly == true) {
                          return Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(24, 4, 24, 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      item.title ?? '',
                                      style: TextStyle(
                                        color: _isLightMode ? AppColors.black : AppColors.white,
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22,
                                      width: 50,
                                      child: FittedBox(
                                        fit: BoxFit.fitHeight,
                                        child: CupertinoSwitch(
                                          activeTrackColor: AppColors.blueShade22,
                                          inactiveTrackColor: AppColors.greySwitchOff,
                                          value: _scadProjectionState.value,
                                          onChanged: (val) {
                                            _scadProjectionState.value = val;

                                            if (val) {
                                              _onDriverUpdated(overridePayloadData: {});
                                            } else {
                                              if (_payload.isEmpty) {
                                                for (int i = 0; i < _indicatorDrivers.length; i++) {
                                                  if (_indicatorDrivers[i].viewOnly != true) {
                                                    _payload[_indicatorDrivers[i].id!] = 'medium';
                                                  }
                                                }
                                              }
                                              _onDriverUpdated(overridePayloadData: _payload);
                                            }

                                            // if (val) {
                                            //   selectedDrivers.value.clear();
                                            //   for (int i = 0; i < indicatorDrivers.length; i++) {
                                            //     if (indicatorDrivers[i].viewOnly != true) {
                                            //       payload[indicatorDrivers[i].id!] = 'medium';
                                            //     }
                                            //     selectedDrivers.value.add(
                                            //       (indicatorDrivers[i].options ?? []).indexWhere(
                                            //             (e) => e.isSelected!,
                                            //       ),
                                            //     );
                                            //   }
                                            //   updateDrivers(payloadData: {});
                                            // } else {
                                            //   for (int i = 0; i < indicatorDrivers.length; i++) {
                                            //   if (indicatorDrivers[i].viewOnly != true) {
                                            //     payload[indicatorDrivers[i].id!] = 'medium';
                                            //   }
                                            //   }
                                            //   updateDrivers(payloadData: payload);
                                            // }
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(color: AppColors.greyShade8),
                              const SizedBox(height: 15),
                            ],
                          );
                        } else {
                          return ValueListenableBuilder(
                            valueListenable: _selectedDrivers,
                            builder: (context, selectedLabels, _) {
                              if (selectedLabels.isEmpty) return const SizedBox();

                              final double value = getValue();
                              return IgnorePointer(
                                ignoring: _scadProjectionState.value,
                                child: Opacity(
                                  opacity: _scadProjectionState.value ? 0.5 : 1,
                                  child: DriverSlider(
                                    key: ValueKey('DriverSlider.${item.id}.$value'),
                                    title: item.title ?? '',
                                    interval: interval,
                                    minValue: _kMinValue,
                                    maxValue: _kMaxValue,
                                    value: value,
                                    labelFormatterCallback: (actualValue, formattedText) {
                                      final i = getOptionIndex(actualValue as double);
                                      return options[i].label?.toLowerCase() == 'no change'
                                          ? '0'
                                          : _isArabic
                                              ? '${transformedOptions[i].label}'
                                              : '${options[i].label}';
                                    },
                                    onChanged: (value) {
                                      final i = getOptionIndex(value);
                                      _payload[_indicatorDrivers[index].id!] = options[i].value ?? '';
                                      _selectedDrivers.value[index] = i;
                                      _onDriverUpdated();
                                    },
                                    selectedLabel: options[selectedLabels[index]].label?.toLowerCase() == 'no change'
                                        ? '0'
                                        : _isArabic
                                            ? transformedOptions[selectedLabels[index]].label
                                            : options[selectedLabels[index]].label,
                                    onMinusIconTap: () {
                                      if (selectedLabels[index] > 0) {
                                        _selectedDrivers.value[index]--;
                                        _payload[_indicatorDrivers[index].id!] = options[selectedLabels[index]].value!;
                                        _onDriverUpdated();
                                      }
                                    },
                                    onPlusIconTap: () {
                                      if (selectedLabels[index] < options.length - 1) {
                                        _selectedDrivers.value[index]++;
                                        _payload[_indicatorDrivers[index].id!] = options[selectedLabels[index]].value!;
                                        _onDriverUpdated();
                                      }
                                    },
                                  ),
                                ),
                              );
                            },
                          );
                        }
                      },
                    );
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 12, 24, 24),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(43),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: _onApplyChanges,
                child: Text(
                  LocaleKeys.apply.tr(),
                  style: const TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _treeMapChartRepresentation() {
    final List<SeriesMeta> treeMapSeriesMeta =
        indicatorDetails.getFilteredVisualizationMetaList().firstOrNull?.seriesMeta ?? [];

    return FittedBox(
      child: SizedBox(
        height: 210 * max(1, HiveUtilsSettings.textSizeFactor),
        width: MediaQuery.sizeOf(context).width * 0.9,
        child: TreemapChart(
          key: Key('treemap.${treeMapSeriesMeta.map((e) => e.color).join()}'),
          chartSeriesData: treeMapSeriesMeta,
        ),
      ),
    );
  }

  Widget _splineChart() {
    final list = indicatorDetails.getFilteredSeriesForMultiDrivers(
      _selectedTabIndex.value,
    );
    final indicatorDateSetting = IndicatorDateSetting.setFrequancy(
      l: list,
      indicatorDetails: IndicatorDetailsResponseHelper(indicatorDetails),
    );

    final String selectedFrequencyForFilter = indicatorDateSetting['selectedFrequencyForFilter'] as String;

    final List<List<SplineChartData>> nowCast = [];
    final List<List<SplineChartData>> foreCast = [];
    final List<List<SplineChartData>> lowerAreaForecast = [];
    final List<List<SplineChartData>> upperAreaForecast = [];
    List<List<SplineChartData>> areaForecast = [];

    final List<SeriesMeta> seriesMeta =
        indicatorDetails.indicatorVisualizations!.visualizationsMeta?.firstOrNull?.seriesMeta ?? [];

    for (int i = 0; i < list.length; i++) {
      if (seriesMeta[i].id!.contains('-forecast')) {
        foreCast.add(
          list[i].map((e) => SplineChartData(e['OBS_DT'].toString(), num.parse('${e['VALUE'] ?? '0'}'))).toList(),
        );
        for (int i = 0; i < list.length; i++) {
          final List<SplineChartData> data = [];

          for (final e in list[i]) {
            if (e['VALUE_LL'] != null) {
              data.add(
                SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                  y: num.parse('${e['VALUE_LL'] ?? '0'}'),
                ),
              );
            }
          }

          lowerAreaForecast.add(data);
        }

        for (int i = 0; i < list.length; i++) {
          final List<SplineChartData> data = [];

          for (final e in list[i]) {
            if (e['VALUE_UL'] != null) {
              data.add(
                SplineChartData(
                  e['OBS_DT'].toString(),
                  num.parse('${e['VALUE'] ?? '0'}'),
                  y: num.parse('${e['VALUE_UL'] ?? '0'}'),
                ),
              );
            }
          }

          upperAreaForecast.add(data);
        }
        areaForecast = [...lowerAreaForecast, ...upperAreaForecast];
      } else {
        nowCast.add(
          list[i]
              .map(
                (e) => SplineChartData(e['OBS_DT'].toString(), num.parse('${e['VALUE'] ?? '0'}')),
              )
              .toList(),
        );
      }
    }

    return SizedBox(
      height: 200,
      child: SplineChart(
        indicatorCard: _isLightMode,
        frequency: selectedFrequencyForFilter,
        showForecast: true,
        chartDataList: nowCast,
        forecastChartDataList: foreCast,
        areaHighlightChartData: areaForecast,
        driver: true,
        yAxisLabel: indicatorDetails.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.yAxisLabel,
      ),
    );
  }

  void _onDriverUpdated({Map<String, String>? overridePayloadData}) {
    context.read<ScenarioForecastDetailsBloc>().add(
          ScenarioDriverChangeEvent(
            id: indicatorDetails.id!,
            contentType: indicatorDetails.indicatorType ?? widget.contentType,
            payload: overridePayloadData ?? _payload,
            isDriverChange: true,
          ),
        );
  }

  void _onApplyChanges() {
    context.maybePop();
    context.read<ScenarioForecastDetailsBloc>().add(
          UpdatedDriverApplyEvent(
            indicatorDetails: indicatorDetails,
            payload: _payload,
            scadProjectionState: _scadProjectionState.value,
          ),
        );
  }
}
