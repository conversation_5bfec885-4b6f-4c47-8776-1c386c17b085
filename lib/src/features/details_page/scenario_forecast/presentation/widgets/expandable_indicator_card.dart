import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/chart_legend.dart';
import 'package:scad_mobile/src/common/widgets/icon_and_title_widget.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_value_v2.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/base/constants.dart';
import 'package:scad_mobile/src/features/details_page/scenario_forecast/presentation/bloc/scenario_forecast_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_data_frequency_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_data_presentation_bottom_sheet.dart';
import 'package:scad_mobile/src/features/details_page/widgets/change_driver_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_action_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_view/chart_view.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chart_view/no_chart_data.dart';
import 'package:scad_mobile/src/features/details_page/widgets/chat_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/download_as/download_as.dart';
import 'package:scad_mobile/src/features/details_page/widgets/full_screen_chart_view/full_screen_chart_view.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_chart_header.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_filter/indicator_filter_button.dart';
import 'package:scad_mobile/src/features/details_page/widgets/indicator_type_tag.dart';
import 'package:scad_mobile/src/features/details_page/widgets/meta_data_widget.dart';
import 'package:scad_mobile/src/features/details_page/widgets/toggle_switch.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/date_time_extensions.dart';
import 'package:scad_mobile/src/utils/extentions/list_extensions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';

class ExpandableIndicatorCard extends StatefulWidget {
  const ExpandableIndicatorCard({
    required this.index,
    required this.id,
    required this.title,
    required this.contentType,
    required this.indicatorType,
    required this.currentIndicatorDetails,
    required this.changeDriverPayload,
    required this.isScadProjectionOn,
    super.key,
  });

  final int index;
  final String id;
  final String title;
  final String contentType;
  final IndicatorType indicatorType;
  final IndicatorDetailsResponse currentIndicatorDetails;
  final Map<String, String> changeDriverPayload;
  final bool isScadProjectionOn;

  @override
  State<ExpandableIndicatorCard> createState() => _ExpandableIndicatorCardState();
}

class _ExpandableIndicatorCardState extends State<ExpandableIndicatorCard> {
  final _isLightMode = HiveUtilsSettings.isLightMode;

  final _screenshotController = ScreenshotController();

  final ValueNotifier<bool> _isExpanded = ValueNotifier(false);

  final ValueNotifier<bool> _isForecastVisibilityOn = ValueNotifier(true);

  ChartDataRepresentation _selectedChartRepresentation = ChartDataRepresentation.line;
  final ValueNotifier<ChartDataRepresentation?> _chartRepresentationPreviewNotifier = ValueNotifier(null);

  final ValueNotifier<List<Options>> _periodOptionList = ValueNotifier([]);

  SeriesDataList filteredSolidData = [];
  SeriesDataList filteredForecastData = [];

  IndicatorDetailsResponse get _indicatorDetails => widget.currentIndicatorDetails;

  VisualizationsMeta? get _currentVisualizationsMeta =>
      _indicatorDetails.indicatorVisualizations?.visualizationsMeta?[widget.index];

  bool get _isWhatIfIndicator => _indicatorDetails.type == 'Internal' && _indicatorDetails.multiDrivers == true;

  bool get _isTreeMapChart => _currentVisualizationsMeta?.type == 'tree-map-with-change-chart';

  SeriesDataList get seriesList => [...filteredSolidData, ...filteredForecastData];

  @override
  void initState() {
    super.initState();

    _isExpanded.value = !_isWhatIfIndicator;
    _checkAndSetTreemapChartType();
    _setCurrentChartData();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isWhatIfIndicator) {
      return _buildContent();
    }

    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 30, right: 20, left: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.greyShade6.withAlpha((0.08 * 255).toInt()),
                        blurRadius: 50,
                        offset: const Offset(0, 35),
                        spreadRadius: -23,
                      ),
                    ],
                  ),
                  child: Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    margin: EdgeInsets.zero,
                    color: _isLightMode ? AppColors.white : AppColors.blueShade32,
                    elevation: 0,
                    child: _buildContent(),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 20,
          child: SizedBox(
            height: 20,
            width: 40,
            child: ValueListenableBuilder(
              valueListenable: _isExpanded,
              builder: (context, isExpanded, child) {
                return ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blueShade27,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: EdgeInsets.zero,
                  ),
                  onPressed: () => _isExpanded.value = !_isExpanded.value,
                  child: Icon(
                    isExpanded ? Icons.keyboard_arrow_up_rounded : Icons.keyboard_arrow_down_rounded,
                    color: Colors.white,
                    size: 22,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildChartHeader(),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              IndicatorValueV2(
                isCardView: false,
                indicatorDetails: _indicatorDetails,
              ),
              const SizedBox(height: 15),
              Screenshot(
                key: Key('scenario.drivers.details.chart.${widget.id}'),
                controller: _screenshotController,
                child: ConstrainedBox(
                  constraints: const BoxConstraints(minHeight: 200),
                  child: _buildChartAndLegends(),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: _isExpanded,
                builder: (context, _, __) => AnimatedSize(
                  duration: const Duration(milliseconds: 200),
                  child: SizedBox(
                    height: _isExpanded.value ? null : 0,
                    child: Column(
                      children: [
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildPeriodFilter(),
                            _buildChatButton(widget.index),
                          ],
                        ),
                        const SizedBox(height: 20),
                        _buildActionButtons(),
                        _buildDownloadAs(),
                        const SizedBox(height: 30),
                        _buildMetaData(),
                        _buildUpdatedOnAndSource(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartHeader() {
    final bloc = context.read<ScenarioForecastDetailsBloc>();
    final filterPanel = bloc.filterPanel;

    final filterBuilder = filterPanel is! FilterPanel
        ? null
        : (BuildContext context) => IndicatorFilterButton(
            adapter: bloc,
            onFilterApplied: (filterMap) {
              context.read<ScenarioForecastDetailsBloc>().add(
                    ApplyForecastFilterEvent(payload: filterMap),
                  );
            },
          );

    Widget driverBuilder(BuildContext context) {
      final driver = _indicatorDetails.indicatorDrivers ?? [];
      if (driver.isEmpty || _isWhatIfIndicator) {
        return const SizedBox.shrink();
      }

      return ChangeDriverButton(
        onPressed: () => AutoRouter.of(context).push(
          ScenarioDriverModifierScreenRoute(
            contentType: widget.contentType,
            indicatorDetails: _indicatorDetails,
            payload: widget.changeDriverPayload,
            isScadProjectionOn: widget.isScadProjectionOn,
            index: widget.index,
          ),
        ),
      );
    }


    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          IndicatorChartHeader(
            filterBuilder: filterBuilder,
            driverBuilder: driverBuilder,
            security: _indicatorDetails.security,
            tag: widget.indicatorType == IndicatorType.scenarioDriver
                ? const IndicatorTypeTag.scenarioDrivers()
                : const IndicatorTypeTag.forecast(),
            fullScreenButtonBuilder: (context) => FullScreenExpandButton(
              onTap: () => FullScreenChartView.show(
                context: context,
                title: widget.title,
                chartBuilder: (context) => _buildChart(isFullScreen: true),
                legendBuilder: (context) => _buildLegends(),
              ),
            ),
          ),
          const SizedBox(height: 10),
          _buildForecastSwitch(),
        ],
      ),
    );
  }

  Widget _buildForecastSwitch() {
    if ((_currentVisualizationsMeta?.seriesMeta ?? []).length > 1 && !_isTreeMapChart) {
      return ToggleSwitch(
        label: LocaleKeys.forecast.tr(),
        initialValue: _isForecastVisibilityOn.value,
        onChanged: (value) => _isForecastVisibilityOn.value = value,
      );
    } else {
      return const SizedBox();
    }
  }

  Widget _buildChatButton(int index) {
    return ChatButton<ScenarioForecastDetailsBloc>(
      key: Key('chat.${widget.index}'),
      title: widget.title,
      contentType: widget.contentType,
      indicatorDetails: _indicatorDetails,
      screenshotController: _screenshotController,
    );
  }

  Widget _buildChartAndLegends({
    bool? enforceLightMode,
    bool showTooltips = false,
  }) {
    if (!_isTreeMapChart && (seriesList.isEmpty || seriesList.every((e) => e.isEmpty))) {
      return const NoChartData();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildChart(
          enforceLightMode: enforceLightMode,
          showTooltips: showTooltips,
        ),
        _buildLegends(enforceLightMode: enforceLightMode),
      ],
    );
  }

  final _seriesDataList = <SeriesData>[];
  final _forecastDataList = <SeriesData>[];

  void _setSeriesLists() {
    final List<SeriesMeta> seriesMetaList = _currentVisualizationsMeta?.seriesMeta ?? [];
    final selectedFilter = context.read<ScenarioForecastDetailsBloc>().dependencyResolvedFilters;

    _seriesDataList.clear();
    _forecastDataList.clear();

    if (selectedFilter.isEmpty) {
      final s = seriesMetaList.where((e) => e.type == 'solid').map((e) => e.data ?? []).toList();
      final f = seriesMetaList.where((e) => e.id?.contains('-forecast') ?? false).map((e) => e.data ?? []).toList();

      _seriesDataList.addAll(s);
      _forecastDataList.addAll(f);
      return;
    }

    final multiSelectFilter = selectedFilter.entries.firstWhere(
      (e) => e.key != kObsDt && e.value.length > 1,
      orElse: () => selectedFilter.entries.firstWhere((e) => e.key != kObsDt),
    );

    final otherFilters = selectedFilter.entries
        .where(
          (e) => e.key != kObsDt && e.key != multiSelectFilter.key,
        )
        .toList();

    for (final option in multiSelectFilter.value) {
      for (final seriesMeta in seriesMetaList) {
        final seriesData = seriesMeta.data;
        final isForecast = seriesMeta.id?.contains('-forecast') ?? false;

        if (seriesData == null || seriesData.isEmpty) {
          isForecast ? _forecastDataList.add([]) : _seriesDataList.add([]);
          continue;
        }

        final series = <JSONObject>[];

        for (final data in seriesData) {
          final isDataOfSelectedLegendFilter =
              data[multiSelectFilter.key].toString().toLowerCase() == option.toLowerCase();
          if (!isDataOfSelectedLegendFilter) continue;

          final isOtherFiltersSatisfied = otherFilters.every(
            (element) => element.value.first.toLowerCase() == data[element.key].toString().toLowerCase(),
          );

          if (isOtherFiltersSatisfied) {
            series.add(data);
          }
        }

        if (isForecast) {
          _forecastDataList.add(series);
        } else {
          _seriesDataList.add(series);
        }
      }
    }
  }

  Widget _buildChart({
    bool? enforceLightMode,
    bool showTooltips = false,
    bool isFullScreen = false,
  }) {
    return BlocBuilder<ScenarioForecastDetailsBloc, DetailsBaseState>(
      buildWhen: (_, state) => state is ApplyForecastFilterSuccessState,
      builder: (context, state) {
        _setCurrentChartData();
        return ValueListenableBuilder(
          valueListenable: _isForecastVisibilityOn,
          builder: (context, isForecastOn, child) {
            return ValueListenableBuilder(
              valueListenable: _periodOptionList,
              builder: (context, periodOptions, child) {
                return ValueListenableBuilder(
                  valueListenable: _chartRepresentationPreviewNotifier,
                  builder: (context, chartPresentationPreview, child) {
                    final chartRepresentation = chartPresentationPreview ?? _selectedChartRepresentation;

                    _setSeriesLists();
                    // final list = _indicatorDetails.getFilteredSeriesForMultiDrivers(widget.index);
                    //
                    // final selectedFilter = context.read<ScenarioForecastDetailsBloc>().dependencyResolvedFilters;
                    //
                    // final nowCast = <SeriesData>[];
                    // final foreCast = <SeriesData>[];
                    //
                    // final List<SeriesMeta> seriesMeta = _currentVisualizationsMeta?.seriesMeta ?? [];
                    //
                    // for (int i = 0; i < list.length; i++) {
                    //   if (seriesMeta[i].id!.contains('-forecast')) {
                    //     foreCast.add(
                    //       list[i]
                    //           .where(
                    //             (data) => selectedFilter.keys.every(
                    //               (key) =>
                    //                   selectedFilter[key]
                    //                       ?.map((e) => e.toLowerCase())
                    //                       .contains(data[key].toLowerCase()) ??
                    //                   false,
                    //             ),
                    //           )
                    //           .toList(),
                    //     );
                    //   } else {
                    //     nowCast.add(list[i]
                    //         .where(
                    //           (data) => selectedFilter.keys.every(
                    //             (key) =>
                    //                 selectedFilter[key]
                    //                     ?.map((e) => e.toLowerCase())
                    //                     .contains(data[key].toLowerCase()) ??
                    //                 false,
                    //           ),
                    //         )
                    //         .toList());
                    //   }
                    // }

                    if (_forecastDataList.isNotEmpty) {
                      for (int i = 0; i < _seriesDataList.length; i++) {
                        final nowSeries = _seriesDataList.elementAt(i);
                        final lastPoint = nowSeries.lastOrNull;
                        if (lastPoint == null) continue;

                        final hash = jsonEncode(lastPoint).hashCode;

                        final forecastSeries = _forecastDataList.elementAt(i);
                        final isConnectedToForecast = forecastSeries.any((e) => jsonEncode(e).hashCode == hash);
                        if (!isConnectedToForecast) {
                          _forecastDataList[i].insert(0, lastPoint);
                        }
                      }
                    }

                    final nowCast1 = <SeriesData>[];
                    for (int i = 0; i < _seriesDataList.length; i++) {
                      nowCast1.add(
                        filterPeriod(_seriesDataList[i]),
                      );
                    }

                    final frequency = IndicatorDateSetting.getChartDataFrequency(
                      seriesList: _seriesDataList,
                      indicatorDetails: _indicatorDetails,
                    );

                    return ChartView(
                      chartRepresentation: chartRepresentation,
                      frequency: frequency,
                      enforceLightMode: enforceLightMode,
                      isFullScreenView: isFullScreen,
                      chartSeriesData: ChartSeriesData(
                        series: nowCast1,
                        forecast: _forecastDataList,
                        // series: filteredSolidData,
                        // forecast: filteredForecastData,
                        seriesMetaList: _currentVisualizationsMeta?.seriesMeta ?? [],
                        tableFields: _indicatorDetails.tableFields,
                      ),
                      showForecast: _isForecastVisibilityOn.value,
                      showTooltips: showTooltips,
                      yAxisLabel: _currentVisualizationsMeta?.yAxisLabel,
                      isTreemapData: _isTreeMapChart,
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildLegends({bool? enforceLightMode}) {
    return ValueListenableBuilder(
      valueListenable: _isForecastVisibilityOn,
      builder: (context, _, __) {
        return ValueListenableBuilder(
          valueListenable: _chartRepresentationPreviewNotifier,
          builder: (context, chartPresentationPreview, child) {
            final chartRepresentation = chartPresentationPreview ?? _selectedChartRepresentation;

            if (chartRepresentation == ChartDataRepresentation.table ||
                chartRepresentation == ChartDataRepresentation.treemap) {
              return const SizedBox();
            }

            final List<String> legends = [];
            final bloc = context.read<ScenarioForecastDetailsBloc>();
            final filterPanel = bloc.filterPanel;

            if (filterPanel != null) {
              final selectedFilter = bloc.selectedFilterMap;
              if (selectedFilter.isNotEmpty) {
                final multiSelectFilter = bloc.getMultiSelectedFilter();

                legends.addAll(multiSelectFilter.value);
              } else {
                legends.add(_indicatorDetails.componentTitle ?? '');
              }
            } else {
              final seriesMeta = _currentVisualizationsMeta?.seriesMeta ?? [];
              legends.addAll(
                seriesMeta.where((e) => e.type == 'solid').map(
                      (e) => (e.label ?? '').toString(),
                    ),
              );
            }

            final isLightMode = enforceLightMode ?? _isLightMode;
            final colorSet = isLightMode ? AppColors.chartColorSet : AppColors.chartColorSetDark;

            return Wrap(
              spacing: 12,
              runSpacing: 8,
              children: legends.indexed
                  .map(
                    (e) {
                      final color = colorSet.elementAt(e.$1 % colorSet.length);
                      return [
                        ChartLegend(
                          label: e.$2,
                          color: color,
                          isLightMode: isLightMode,
                        ),
                        if (_isForecastVisibilityOn.value)
                          ChartLegend(
                            label: '${e.$2}-${LocaleKeys.forecast.tr()}',
                            color: color,
                            isLightMode: isLightMode,
                            isForecast: true,
                          ),
                      ];
                    },
                  )
                  .expand((e) => e)
                  .toList(),
            );
          },
        );
      },
    );
  }

  void _setPeriodFilter() {
    if (_periodOptionList.value.isNotEmpty || _isTreeMapChart) return;

    if (_isWhatIfIndicator) {
      _periodOptionList.value = [
        Options(id: LocaleKeys.all.tr(), label: LocaleKeys.all.tr()),
        Options(id: LocaleKeys.recent.tr(), label: LocaleKeys.recent.tr(), isSelected: true),
      ];
      return;
    }

    final seriesLengths =
        filteredSolidData.indexed.map((e) => e.$2.length + filteredForecastData.elementAt(e.$1).length);
    final bool showRecent = seriesLengths.any((e) => e > 12);

    _periodOptionList.value = [
      ...(_indicatorDetails.indicatorFilters?.firstOrNull?.options ?? []),
      if (showRecent) Options(id: LocaleKeys.recent.tr(), label: LocaleKeys.recent.tr()),
    ];
    _periodOptionList.value
      ..map((e) => e.isSelected = false)
      ..lastOrNull?.isSelected = true;
  }

  void _setCurrentChartData() {
    filteredSolidData = [];
    filteredForecastData = [];

    final selectedFilter = context.read<ScenarioForecastDetailsBloc>().selectedFilterMap;

    for (final seriesMeta in _currentVisualizationsMeta?.seriesMeta ?? <SeriesMeta>[]) {
      final type = seriesMeta.type;
      final seriesData = seriesMeta.data;
      if (seriesData == null || seriesData.isEmpty || type == null) continue;

      final filteredData = seriesData
          .where(
            (data) => selectedFilter.entries.every(
              (entry) => entry.value.any(
                (e) => e.toLowerCase() == (data[entry.key] as String?)?.toLowerCase(),
              ),
            ),
          )
          .toList();

      if (type == 'solid') {
        filteredSolidData.add(filteredData);
      }

      if (type.startsWith('forecast')) {
        filteredForecastData.add(filteredData);
      }
    }

    _setPeriodFilter();

    filteredForecastData = filteredForecastData.map((e) {
      return e
        ..sort(
          (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
        );
    }).toList();

    filteredSolidData = filteredSolidData.indexed.map((e) {
      return filterPeriod(
        e.$2
          ..sort(
            (a, b) => a['OBS_DT'].toString().compareTo(b['OBS_DT'].toString()),
          ),
      );
    }).toList();
  }

  SeriesData filterPeriod(SeriesData sortedNowCast) {
    SeriesData filteredData = [];

    try {
      final period = _periodOptionList.value.firstWhere(
        (e) => e.isSelected,
        orElse: () => _periodOptionList.value.lastOrNull ?? Options(),
      );

      final labelLowerCase = period.label?.toLowerCase();
      if (kAllOptions.contains(labelLowerCase)) {
        filteredData = sortedNowCast;
      } else if (period.label == LocaleKeys.recent.tr()) {
        filteredData = sortedNowCast.limitListLength(limit: 12);
      } else {
        final startDate =
            DateTime.tryParse(_currentVisualizationsMeta?.seriesMeta?.firstOrNull?.xMax ?? '') ?? DateTime.now();

        for (final element in [sortedNowCast]) {
          late DateTime endDate;
          if (period.unit?.toLowerCase() == 'years') {
            endDate = DateTime(startDate.year - (period.value ?? 0), startDate.month, startDate.day);
          } else {
            endDate = DateTime(startDate.year, startDate.month - (period.value ?? 0), startDate.day);
          }

          filteredData.addAll(
            element.where((entry) {
              final obsDate = DateTime.parse(entry['OBS_DT'] as String);
              return obsDate.compareTo(endDate) >= 0 && obsDate.compareTo(startDate) <= 0;
            }).toList(),
          );
        }
      }
    } catch (e, s) {
      AppLog.error(e, s);
    }

    return filteredData;
  }

  void _checkAndSetTreemapChartType() {
    if (_isTreeMapChart) {
      _selectedChartRepresentation = ChartDataRepresentation.treemap;
    }
  }

  Widget _buildMetaData() {
    return MetaDataWidget(
      id: widget.id,
      title: widget.title,
      metaData: _indicatorDetails.metaData,
      margin: const EdgeInsets.only(bottom: 20),
    );
  }

  Widget _buildUpdatedOn() {
    return IconAndTitleWidget(
      icon: AppImages.icCalendar,
      title: LocaleKeys.updatedOn.tr(),
      content: DateTime.tryParse(
            '${_indicatorDetails.publicationDate}',
          )?.toFormattedDateTimeString('dd/MM/yyyy') ??
          _indicatorDetails.publicationDate ??
          '_',
    );
  }

  Widget _buildDataSource() {
    final dataSource = _indicatorDetails.dataSource?.trim() ?? '';
    if (dataSource.isEmpty) {
      return const SizedBox();
    }

    return IconAndTitleWidget(
      icon: AppImages.icDocument,
      title: LocaleKeys.source.tr(),
      content: dataSource,
    );
  }

  Widget _buildUpdatedOnAndSource() {
    if (DeviceType.isTab()) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildUpdatedOn(),
          _buildDataSource(),
        ],
      );
    }

    return Align(
      alignment: DeviceType.isDirectionRTL(context) ? Alignment.centerRight : Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUpdatedOn(),
          const SizedBox(height: 17),
          _buildDataSource(),
          const SizedBox(height: 6),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    if (_isWhatIfIndicator) return const SizedBox();

    final hasNoSeries = _isTreeMapChart
        ? (_currentVisualizationsMeta?.seriesMeta?.isNotEmpty ?? false)
        : seriesList.isNotEmpty && seriesList.any((e) => e.isNotEmpty);
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Expanded(
            child: ChartActionButton.frequency(
              enabled: false,
            ),
          ),
          Expanded(
            child: ChartActionButton.presentation(
              enabled: hasNoSeries,
              onPressed: () async {
                _chartRepresentationPreviewNotifier.value = _selectedChartRepresentation;

                final availableOptions = [
                  if (_isTreeMapChart) ChartDataRepresentation.treemap else ChartDataRepresentation.line,
                  ChartDataRepresentation.table,
                ];

                await SelectDataPresentationBottomSheet.show(
                  context,
                  options: availableOptions,
                  initialRepresentation: _selectedChartRepresentation,
                  onPreview: (value) => _chartRepresentationPreviewNotifier.value = value,
                  onDone: (value) => _selectedChartRepresentation = value,
                );
                _chartRepresentationPreviewNotifier.value = null;
              },
            ),
          ),
          const Expanded(
            child: ChartActionButton.compare(
              enabled: false,
            ),
          ),
          const Expanded(
            child: ChartActionButton.compute(
              enabled: false,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadAs() {
    return ValueListenableBuilder(
      valueListenable: _chartRepresentationPreviewNotifier,
      builder: (context, value, child) {
        final isRestricted = _indicatorDetails.security?.id != '0';
        final ChartDataFrequency frequency =
            IndicatorDateSetting.getChartDataFrequency(seriesList: seriesList, indicatorDetails: _indicatorDetails);

        return DownloadAsV2(
          key: Key('${_selectedChartRepresentation.index}'),
          title: _indicatorDetails.componentTitle ?? '',
          description: _indicatorDetails.componentSubtitle ?? '',
          seriesList: seriesList,
          isRestricted: isRestricted,
          isTableView: _selectedChartRepresentation == ChartDataRepresentation.table,
          tableFieldList: _indicatorDetails.tableFields,
          frequency: frequency,
          chart: _buildChartAndLegends(
            enforceLightMode: true,
            showTooltips: true,
          ),
          // selectedVisualization: _selectedVisualization ?? Visualizations(),
        );
      },
    );
  }

  Widget _buildPeriodFilter() {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: !_isLightMode ? AppColors.blueShade36 : const Color(0xFFD9D9D9),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: _isLightMode ? AppColors.greyF3F4F6 : Colors.transparent,
        ),
      ),
      child: SizedBox(
        // TODO(Jerin): Change this widget to [ChartPeriodSwitcher]
        // height: 28 * textScaleFactor.value,
        height: 32,
        child: ValueListenableBuilder(
          valueListenable: _periodOptionList,
          builder: (context, periodOptionList, __) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(
                periodOptionList.length,
                (index) {
                  return InkWell(
                    onTap: () {
                      if (periodOptionList[index].isSelected) {
                        return;
                      }
                      for (int i = 0; i < periodOptionList.length; i++) {
                        periodOptionList[i].isSelected = i == index;
                      }
                      _periodOptionList.value = [...periodOptionList];
                      _setCurrentChartData();
                    },
                    child: Container(
                      constraints: const BoxConstraints(minWidth: 43),
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: periodOptionList[index].isSelected
                            ? _isLightMode
                                ? AppColors.blueLight
                                : AppColors.blueLightOld
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(40),
                      ),
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: Text(
                          periodOptionList[index].id?.toLowerCase() == LocaleKeys.recent.tr().toLowerCase()
                              ? LocaleKeys.recent.tr()
                              : periodOptionList[index].id?.toLowerCase() == 'all'
                                  ? LocaleKeys.all.tr()
                                  : periodOptionList[index].unit?.toLowerCase() == 'years' ||
                                          periodOptionList[index].unit?.toLowerCase() == 'year'
                                      ? '${periodOptionList[index].value}Y'
                                      : '${periodOptionList[index].value}M',
                          style: TextStyle(
                            color: periodOptionList[index].isSelected
                                ? AppColors.white
                                : _isLightMode
                                    ? AppColors.blackShade4
                                    : AppColors.greyShade4,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
