import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

class AppSliderThumbShape extends SfDividerShape {
  @override
  void paint(
    PaintingContext context,
    Offset center,
    Offset? thumbCenter,
    Offset? startThumbCenter,
    Offset? endThumbCenter, {
    required RenderBox parentBox,
    required SfSliderThemeData themeData,
    required Paint? paint,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    SfRangeValues? currentValues,
    dynamic currentValue,
  }) {
    context.canvas.drawOval(
      Rect.fromCenter(center: center, width: 10, height: 10),
      Paint()
        ..isAntiAlias = true
        ..style = PaintingStyle.fill
        ..color = Colors.white,
    );
    context.canvas.drawOval(
      Rect.fromCenter(center: center, width: 10, height: 10),
      Paint()
        ..isAntiAlias = true
        ..style = PaintingStyle.fill
        ..color = themeData.activeTrackColor!,
    );
  }
}
