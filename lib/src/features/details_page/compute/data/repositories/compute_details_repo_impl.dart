import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details_page/compute/data/data_sources/compute_endpoints.dart';
import 'package:scad_mobile/src/features/details_page/compute/domain/repositories/compute_details_repository.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';

class ComputeDetailsRepositoryImpl extends ComputeDetailsRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> compute({required JSONObject payload}) {
    final endpoint = ComputeDetailsEndPoints.computeDataEndPoint;
    final cacheKey = getCacheKey(endpoint, payload: payload);
    return fetchWithCache<IndicatorDetailsResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: payload,
      ),
      parseResult: (json) => IndicatorDetailsResponse.fromJson(json),
    );
  }
}