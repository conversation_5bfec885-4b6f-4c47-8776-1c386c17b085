import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/custom_animated_expansion_tile.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/features/details_page/base/helper/route_helper.dart';
import 'package:scad_mobile/src/features/details_page/compute/data/enums/compute_operations.dart';
import 'package:scad_mobile/src/features/details_page/compute/data/models/compute_data_params.dart';
import 'package:scad_mobile/src/features/details_page/compute/presentation/%20bloc/compute_details_bloc.dart';
import 'package:scad_mobile/src/features/details_page/widgets/bottom_sheet/select_compute_operation_bottom_sheet.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class SelectComputableScreen extends StatefulWidget {
  const SelectComputableScreen({
    required this.indicatorId,
    required this.operation,
    required this.propertiesList,
    required this.security,
    super.key,
  });

  final String indicatorId;
  final ComputeOperations operation;
  final List<Properties> propertiesList;
  final Security? security;

  @override
  State<SelectComputableScreen> createState() => _SelectComputableScreenState();
}

class _SelectComputableScreenState extends State<SelectComputableScreen> {
  final isLightMode = HiveUtilsSettings.isLightMode;

  late final _computeDataParams = ComputeDataParams(
    operation: widget.operation,
    indicatorId: widget.indicatorId,
    properties: widget.propertiesList,
  );

  final _nameController = TextEditingController();

  Future<void> _onComputationTitleTapped() async {
    final result = await SelectComputeOperationBottomSheet.show(
      context,
      initialOperation: _computeDataParams.operation,
    );

    if (result == null) return;
    _computeDataParams.operation = result;
  }

  void _onFirstPropertyChanged(int? value) {
    if (value == null) return;

    final currentIndex = _computeDataParams.firstSelectedOptionIndex;
    if (currentIndex == value) return;

    _computeDataParams.firstSelectedOptionIndex = value;
  }

  void _computeBlocListener(BuildContext context, ComputeDetailsState state) {
    if (state is GetComputeDetailsSuccessState) {
      final title = _nameController.text.trim();

      DetailsPageRouteHelper.computeIndicatorDetailsPage(
        context,
        title: title,
        indicatorDetails: state.data,
        security: widget.security,
      );
    }

    if (state is GetComputeDetailsErrorState) {
      AppMessage.showOverlayNotificationError(message: state.error);
    }
  }

  void _onComputeTapped(BuildContext context) {
    if (_nameController.text.trim().isEmpty) {
      return AppMessage.showOverlayNotificationError(
        message: LocaleKeys.computeValidation.tr(),
      );
    }

    if (_computeDataParams.selectedPropertyIndex == null || _computeDataParams.firstSelectedOptionIndex == null) {
      return AppMessage.showOverlayNotificationError(
        message: LocaleKeys.theFirstIndicatorIsrequired.tr(),
      );
    }
    if (_computeDataParams.secondSelectedOptionIndex == null) {
      return AppMessage.showOverlayNotificationError(
        message: LocaleKeys.atLeastOneSecondIndicatorShouldBeSelected.tr(),
      );
    }

    try {
      final payload = _computeDataParams.getPayload();
      context.read<ComputeDetailsBloc>().add(
            GetComputedDataEvent(
              name: _nameController.text,
              payload: payload,
            ),
          );
    } catch (e, s) {
      AppLog.error(e, s);
      AppMessage.showOverlayNotificationError(
        message: e.toString(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ComputeDetailsBloc(),
      lazy: false,
      child: AppDrawer(
        child: Scaffold(
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              FlatAppBar(
                title: LocaleKeys.computation.tr(),
                bottomPadding: 0,
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: _buildComputationTitle(),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListenableBuilder(
                  listenable: _computeDataParams,
                  builder: (context, child) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildFirstIndicatorDropdown(),
                          const SizedBox(height: 30),
                          Text(
                            LocaleKeys.selectOtherIndicators.tr(),
                            style: TextStyle(
                              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const SizedBox(height: 10),
                          ..._computeDataParams.properties.indexed.map(
                            (entry) => _buildOtherPropertyDropdown(entry.$1, entry.$2),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 16),
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: LocaleKeys.enterNewIndicatorName.tr(),
                            style: TextStyle(
                              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          const WidgetSpan(
                            child: SizedBox(width: 5),
                          ),
                          const TextSpan(
                            text: '*',
                            style: TextStyle(
                              color: Color(0xFFBA0202),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),

                    /// compute indicator name
                    SizedBox(
                      height: 49,
                      child: _buildComputeNameTextField(),
                    ),
                    const SizedBox(height: 30),
                    BlocConsumer<ComputeDetailsBloc, ComputeDetailsState>(
                      listener: _computeBlocListener,
                      builder: (context, state) {
                        if (state is GetComputeDetailsLoadingState) {
                          return const Center(
                            child: SizedBox.square(
                              dimension: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                              ),
                            ),
                          );
                        }

                        return ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size.fromHeight(43),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onPressed: () => _onComputeTapped(context),
                          child: Text(
                            LocaleKeys.compute.tr(),
                            style: const TextStyle(
                              color: AppColors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildComputationTitle() {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return ListenableBuilder(
      listenable: _computeDataParams,
      builder: (context, child) => Material(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(10),
        child: InkWell(
          onTap: _onComputationTitleTapped,
          borderRadius: BorderRadius.circular(10),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 10,
              vertical: 8,
            ),
            child: Center(
              child: Text(
                _computeDataParams.operation.label,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFirstIndicatorDropdown() {
    final selectedProperty = _computeDataParams.selectedProperty;
    final options = _computeDataParams.firstPropertyOptions;
    final selectedOptionIndex = _computeDataParams.firstSelectedOptionIndex;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(20),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow1,
            blurRadius: 5,
            offset: Offset(1, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RoundedDropDownWidget<Properties>(
            compute: true,
            title: LocaleKeys.selectFirstIndicator.tr(),
            items: widget.propertiesList,
            value: selectedProperty,
            onChanged: _computeDataParams.onSelectedPropertyChanged,
          ),
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.fastLinearToSlowEaseIn,
            child: Center(
              child: options == null
                  ? const SizedBox.shrink()
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 20),
                        ...options.indexed.map((e) {
                          final index = e.$1;
                          final option = e.$2;

                          return RadioListTile<int>(
                            activeColor: isLightMode ? AppColors.blueShade22 : AppColors.blueLightOld,
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            value: index,
                            groupValue: selectedOptionIndex,
                            onChanged: _onFirstPropertyChanged,
                            title: Text(
                              option,
                              style: TextStyle(
                                color: isLightMode ? AppColors.grey : AppColors.greyShade2,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            contentPadding: EdgeInsets.zero,
                            visualDensity: VisualDensity.compact,
                          );
                        }),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOtherPropertyDropdown(int index, Properties property) {
    final selectedPropertyIndex = _computeDataParams.selectedPropertyIndex;
    final firstOptionIndex = _computeDataParams.firstSelectedOptionIndex;

    final isEnabled = selectedPropertyIndex == index && firstOptionIndex != null;
    final options = property.options ?? [];

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: isEnabled
            ? isLightMode
                ? AppColors.greyShade7
                : AppColors.blueShade32
            : AppColors.greyShade13,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: isLightMode ? AppColors.shadow1 : Colors.transparent,
            blurRadius: 5,
            offset: const Offset(1, 4),
          ),
        ],
      ),
      child: CustomAnimatedExpansionTile(
        title: property.label ?? '',
        isExpanded: isEnabled,
        onExpansionChanged: () {},
        children: [
          const Divider(color: AppColors.greyShade1),
          ...options.indexed.map(
            (e) {
              final index = e.$1;
              final option = e.$2;

              final isDisabled = _computeDataParams.firstSelectedOptionIndex == index;
              final isChecked = _computeDataParams.secondSelectedOptionIndex == index;

              return IgnorePointer(
                ignoring: isDisabled,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: CheckboxListTile(
                    checkColor: AppColors.white,
                    activeColor: isLightMode ? AppColors.blueShade22 : AppColors.blueLightOld,
                    enabled: !isDisabled,
                    value: isChecked,
                    onChanged: (bool? value) {
                      if (value == null) return;
                      _computeDataParams.secondSelectedOptionIndex = value ? index : null;
                    },
                    title: Text(
                      option,
                      style: TextStyle(
                        color: isLightMode ? AppColors.grey : AppColors.greyShade2,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    contentPadding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                    controlAffinity: ListTileControlAffinity.leading,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildComputeNameTextField() {
    return TextField(
      controller: _nameController,
      textCapitalization: TextCapitalization.sentences,
      maxLength: 60,
      style: TextStyle(
        color: isLightMode ? AppColors.black : AppColors.white,
        fontSize: 16,
      ),
      decoration: InputDecoration(
        fillColor: isLightMode ? AppColors.white : AppColors.blueShade32,
        filled: true,
        counterText: '',
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 12,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: AppColors.greyShade1,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: AppColors.greyShade1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: AppColors.greyShade1,
          ),
        ),
        hintText: LocaleKeys.enterHere.tr(),
        hintStyle: TextStyle(
          color: AppColors.greyShade1,
          fontWeight: FontWeight.w400,
          fontSize: 16,
        ),
      ),
      onSubmitted: (_) {
        FocusManager.instance.primaryFocus?.unfocus();
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _computeDataParams.dispose();
    _nameController.dispose();
  }
}
