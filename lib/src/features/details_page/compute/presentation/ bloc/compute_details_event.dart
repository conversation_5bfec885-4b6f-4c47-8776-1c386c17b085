part of 'compute_details_bloc.dart';

abstract class ComputeDetailsEvent extends Equatable {
  const ComputeDetailsEvent();

  @override
  List<Object> get props => [];
}

class GetComputedDataEvent extends ComputeDetailsEvent {
  const GetComputedDataEvent({
    required this.name,
    required this.payload,
  });
  
  final String name;
  final JSONObject payload;

  @override
  List<Object> get props => [payload];
}
