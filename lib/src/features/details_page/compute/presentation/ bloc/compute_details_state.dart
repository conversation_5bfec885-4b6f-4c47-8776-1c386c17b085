part of 'compute_details_bloc.dart';

abstract class ComputeDetailsState extends Equatable {
  const ComputeDetailsState();

  @override
  List<Object> get props => [];
}

class ComputeDetailsVoidState extends ComputeDetailsState {}

class GetComputeDetailsLoadingState extends ComputeDetailsState {}

class GetComputeDetailsSuccessState extends ComputeDetailsState {
  const GetComputeDetailsSuccessState({
    required this.computeName,
    required this.data,
  });

  final String computeName;
  final IndicatorDetailsResponse data;

  @override
  List<Object> get props => [data];
}

class GetComputeDetailsErrorState extends ComputeDetailsState {
  const GetComputeDetailsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}
