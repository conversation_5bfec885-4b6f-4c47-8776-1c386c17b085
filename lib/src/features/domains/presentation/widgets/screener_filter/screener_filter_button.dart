import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/multi_select/models/value_item.dart';
import 'package:scad_mobile/src/common/widgets/multi_select/multiselect_dropdown.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'screener_filter_bottom_sheet.dart';

typedef SelectedScreenerMap = Map<String, List<Items>>;

typedef OnScreenerFilterChanged = void Function(SelectedScreenerMap screenerMap);

SelectedScreenerMap _getDefaultScreenMap(List<ExperimentalFiltersResponseItem> screener) {
  final SelectedScreenerMap defaultScreenerFilter = {};
  for (final item in screener) {
    if (item.defaultVal != null) {
      defaultScreenerFilter[item.key!] = [item.defaultVal!];
    }
  }
  return defaultScreenerFilter;
}

class ScreenerFilterButton extends StatefulWidget {
  const ScreenerFilterButton({
    required this.screener,
    required this.onChanged,
    this.initialSelectedScreenerMap,
    super.key,
  });

  final List<ExperimentalFiltersResponseItem> screener;
  final SelectedScreenerMap? initialSelectedScreenerMap;
  final OnScreenerFilterChanged onChanged;

  @override
  State<ScreenerFilterButton> createState() => _ScreenerFilterButtonState();
}

class _ScreenerFilterButtonState extends State<ScreenerFilterButton> {
  late final SelectedScreenerMap? _selectedScreenerMap = widget.initialSelectedScreenerMap;

  Future<void> _onTap(BuildContext context) async {
    SelectedScreenerMap? initialScreenerMap = _selectedScreenerMap;
    if (initialScreenerMap == null || initialScreenerMap.isEmpty) {
      initialScreenerMap = _getDefaultScreenMap(widget.screener);
    }

    final result = await ScreenerFilterBottomSheet.show(
      context,
      screener: widget.screener,
      initialSelectedScreenerMap: SelectedScreenerMap.from(initialScreenerMap),
    );

    if (result == null) return;

    _selectedScreenerMap
      ?..clear()
      ..addAll(result);

    widget.onChanged(result);
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 12),
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: () => _onTap(context),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    AppImages.icFilters,
                    colorFilter: ColorFilter.mode(
                      isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                      BlendMode.srcIn,
                    ),
                  ),
                  const SizedBox(width: 5),
                  Text(
                    LocaleKeys.filters.tr(),
                    style: TextStyle(
                      color: isLightMode ? AppColors.black : AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
