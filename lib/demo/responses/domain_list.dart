part of '../demo_api_responses.dart';

final demoDomainModelResponse = [
  {
    'uuid': '45afcbc3-cd30-4d89-b2a8-080c9d128aef',
    'added_by': {
      'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c',
      'name': 'Admin',
    },
    'edited_by': {
      'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c',
      'name': 'Admin',
    },
    'domain_icon': 'https://scadmobile.beinex.com/data/domain_icon/bayan_8d06d737-f4d3-43bc-9f9a-72b1a7697752.svg',
    'domain_name_ar': 'الإحصاءات السكانية والديموغرافية',
    'domain_id': '574',
    'id': '574',
    'domain_name': 'Population & Demographic',
    'name': 'Population & Demographic',
    'active': true,
    'created_time': '2024-02-17T04:14:26.326401Z',
    'updated_time': '2024-03-04T07:15:40.672979Z',
  },
  {
    'uuid': '4cd55d76-acdf-4bcc-ac9e-1e7db1aa5a5e',
    'added_by': {
      'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c',
      'name': 'Admin',
    },
    'edited_by': {
      'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c',
      'name': 'Admin',
    },
    'domain_icon':
        'https://scadmobile.beinex.com/data/domain_icon/rejected-chat_9415dd69-3b65-48fa-afcd-affcce346ec8.svg',
    'domain_name_ar': 'الإحصاءات الاقتصادية',
    'domain_id': '575',
    'id': '575',
    'domain_name': 'Economy',
    'name': 'Economy',
    'active': true,
    'created_time': '2024-01-19T11:20:17.402182Z',
    'updated_time': '2024-02-21T04:21:30.743568Z',
  },
  {
    'uuid': '67822c12-e9c6-443a-bd9e-9de66d89561e',
    'added_by': {'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c', 'name': 'Admin'},
    'edited_by': {'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c', 'name': 'Admin'},
    'domain_icon':
        'https://scadmobile.beinex.com/data/domain_icon/Environment_02df38c4-3eb2-44f5-afb1-dd15ca33ba96.svg',
    'domain_name_ar': 'الإحصاءات الزراعية و البيئية',
    'domain_id': '581',
    'id': '581',
    'domain_name': 'Agriculture & Environment',
    'name': 'Agriculture & Environment',
    'active': true,
    'created_time': '2024-01-17T05:31:54.351293Z',
    'updated_time': '2024-01-18T10:17:20.126715Z',
  },
  {
    'uuid': 'd64d5e3c-c915-4636-90a4-************',
    'added_by': {
      'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c',
      'name': 'Admin',
    },
    'edited_by': {
      'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c',
      'name': 'Admin',
    },
    'domain_icon':
        'https://scadmobile.beinex.com/data/domain_icon/Population (1)_cda4cd8d-0d66-47dc-8b72-1f89b1348a64.svg',
    'domain_name_ar': 'إحصاءات القوى العاملة',
    'domain_id': '579',
    'id': '579',
    'domain_name': 'Labour Force',
    'name': 'Labour Force',
    'active': true,
    'created_time': '2024-01-17T05:29:18.064012Z',
    'updated_time': '2024-01-18T10:14:23.814409Z',
  },
  {
    'uuid': '1c260216-5db2-4b76-8e01-8769b05d3e4f',
    'added_by': {'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c', 'name': 'Admin'},
    'edited_by': {'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c', 'name': 'Admin'},
    'domain_icon': 'https://scadmobile.beinex.com/data/domain_icon/Globe_23f4b174-67e6-4527-ba71-98a143a0ba4b.svg',
    'domain_name_ar': 'الإحصاءات الاجتماعية',
    'domain_id': '3198',
    'id': '3198',
    'domain_name': 'Social Statistics',
    'name': 'Social Statistics',
    'active': true,
    'created_time': '2024-01-06T08:30:41.897230Z',
    'updated_time': '2024-01-25T10:07:51.803735Z',
  },
  {
    'uuid': 'a52b7e84-d563-482e-aa71-ba6a27f317e6',
    'added_by': {'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c', 'name': 'Admin'},
    'edited_by': {'uuid': 'f37f8c39-bb76-4049-a33b-9ec2cbc0069c', 'name': 'Admin'},
    'domain_icon': 'https://scadmobile.beinex.com/data/domain_icon/Male_b248b240-43b0-490d-9eae-ae8639ebbb41.svg',
    'domain_name_ar': 'إحصاءات قطاع الأعمال',
    'domain_id': '3199',
    'id': '3199',
    'domain_name': 'Industry & Business',
    'name': 'Industry & Business',
    'active': true,
    'created_time': '2024-01-06T08:27:25.396535Z',
    'updated_time': '2024-02-16T14:36:38.401216Z',
  }
];

Map<String, Object> demoDomainClassificationsResponse(String id) => {
      'classification': [
        {
          'id': '510',
          'key': 'official_statistics',
          'name': 'Official Statistics',
          'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/Official%20indicators_new.svg',
          'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/Official%20indicators%20new.svg',
          'count': 63,
        },
        {
          'id': '509',
          'key': 'experimental_statistics',
          'name': 'Experimental Statistics',
          'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/IFP%20indicators%20New.svg',
          'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/IFP%20indicators_new.svg',
          'count': '324228773957',
        },
      ],
      'domain': demoDomainModelResponse.firstWhere((element) => element['id'] == id),
    };

List<Map<String, Object>> domainThemes(String domainId, String classificationId) {
  if (domainId == '574') {
    if (classificationId == '510') {
      return [
        {
          'id': '4794',
          'name': 'Population',
          'showScreener': false,
          'subthemes': [
            {'id': '3227', 'name': 'Population Statistics', 'showScreener': false},
            {'id': '3251', 'name': 'Vital Statistics', 'showScreener': false},
          ],
        }
      ];
    } else if (classificationId == '509') {
      return [
        {
          'id': '577',
          'name': 'Population Projections',
          'showScreener': true,
          'screenerConfiguration': {
            'screenerView': 'VW_IFP_IND_POP_CARD_VALUES',
            'screenerIndicator': 'IFP_IND_POP',
          },
          'subthemes': [
            {'id': '3724', 'name': 'Population', 'showScreener': false},
          ],
        }
      ];
    }
  } else if (domainId == '575') {
    if (classificationId == '510') {
      return [
        {
          'id': '3205',
          'name': 'Foreign Trade & FDI',
          'showScreener': false,
          'subthemes': [
            {'id': '3245', 'name': 'Merchandise Trade', 'showScreener': false},
          ],
        },
        {
          'id': '578',
          'name': 'National Accounts',
          'showScreener': false,
          'subthemes': [
            {'id': '3193', 'name': 'Annual National Accounts', 'showScreener': false},
          ],
        },
        {
          'id': '592',
          'name': 'Prices',
          'showScreener': false,
          'subthemes': [
            {'id': '3243', 'name': 'IPI And Other Indices', 'showScreener': false},
          ],
        },
        {
          'id': '3208',
          'name': 'Productivity',
          'showScreener': false,
          'subthemes': [
            {'id': '3282', 'name': 'Labor Productivity', 'showScreener': false},
          ],
        },
        {
          'id': '3206',
          'name': 'Public Sector',
          'showScreener': false,
          'subthemes': [
            {
              'id': '3263',
              'name': 'AD Government Finance',
              'showScreener': true,
              'screenerConfiguration': {
                'screenerView': 'VW_STAT_IND_SCREENER',
                'screenerIndicator': 'AD Government Finance',
                'screenerFilterBy': {
                  'TOPIC_NAME_ENGLISH': ['Economy'],
                  'THEME_NAME_ENGLISH': ['Public Sector'],
                  'SUB_THEME_NAME_ENGLISH': ['AD Government Finance'],
                },
              },
            }
          ],
        }
      ];
    } else if (classificationId == '509') {
      return [
        {
          'id': '4864',
          'name': 'Abu Dhabi Awarded Projects',
          'showScreener': true,
          'screenerConfiguration': {
            'screenerView': 'VW_IFP_IND_MEED_CARD_VALUES',
            'screenerIndicator': 'IFP_IND_MEED',
          },
          'subthemes': [
            {
              'id': '3948',
              'name': 'Consumer spending',
              'showScreener': false,
            },
          ],
        },
        {
          'id': '3947',
          'name': 'Consumer Spending - NI',
          'showScreener': true,
          'screenerConfiguration': {'screenerView': 'VW_IFP_IND_NI_CARD_VALUES', 'screenerIndicator': 'IFP_IND_NI'},
          'subthemes': [
            {
              'id': '3948',
              'name': 'Consumer spending',
              'showScreener': false,
            },
          ],
        },
        {
          'id': '4648',
          'name': 'Consumer Spending - VISA',
          'showScreener': true,
          'screenerConfiguration': {
            'screenerView': 'VW_IFP_IND_VISA_CARD_VALUES',
            'screenerIndicator': 'IFP_IND_VISA',
            'domain': 'Economy',
            'product': 'Visa',
            'subdomain': 'Consumer Spending - VISA',
            'subtheme': 'VISA',
          },
          'subthemes': [
            {'id': '4649', 'name': 'VISA', 'showScreener': false},
          ],
        }
      ];
    }
  } else if (domainId == '579') {
    if (classificationId == '510') {
      return [
        {
          'id': '583',
          'name': 'Environment',
          'showScreener': false,
          'subthemes': [
            {
              'id': '3215',
              'name': 'Water (Management & Desalination)',
              'showScreener': false,
            },
          ],
        }
      ];
    } else if (classificationId == '509') {
      return [];
    }
  } else if (domainId == '3198') {
    if (classificationId == '510') {
      return [
        {
          'id': '576',
          'name': 'Education',
          'showScreener': false,
          'subthemes': [
            {
              'id': '3270',
              'name': 'Literacy',
              'showScreener': false,
            },
          ],
        },
        {
          'id': '3202',
          'name': 'Social protection & wellbeing',
          'showScreener': false,
          'subthemes': [
            {
              'id': '3276',
              'name': 'Tolerance',
              'showScreener': false,
            },
          ],
        },
      ];
    } else if (classificationId == '509') {
      return [];
    }
  }
  return [];
}
