module Fastlane
  module Actions
    module SharedValues
    end

    class UpdatePubspecVersionAction < Action
      def self.run(params)
        version = params[:new_version]
        path = params[:path]
        content = File.read(path).gsub(/version: .+/, "version: #{version}")
        File.open("pubspec.yaml.tmp", "w") { |f| f.write(content) }
        FileUtils.mv("pubspec.yaml.tmp", path)
        UI.success "Updated pubspec successfully"
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        'Update version string in pubspec.yaml of your flutter project'
      end

      def self.details
        'To get the version from pubspec.yaml, checkout `fastlane action get_pubspec_version`'
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(key: :new_version,
                                       env_name: 'FL_FLUTTER_NEW_VERSION',
                                       description: 'New version that will replaced in pubspec.yaml',
                                       is_string: true,
                                       verify_block: proc do |value|
                                         unless value && !value.empty?
                                           UI.user_error!("Please pass a new version, pass using `new_version: 'token'`")
                                         end
                                       end),
          FastlaneCore::ConfigItem.new(key: :path,
                                       env_name: 'FL_PUBSPEC_PATH',
                                       description: 'Path to your `pubspec.yaml` file',
                                       is_string: true,
                                       verify_block: proc do |value|
                                         unless value && !value.empty?
                                           UI.user_error!("Please pass path to pubspec.yaml file, pass using `path: '/path/to/pubspec.yaml'`")
                                         end
                                       end)
        ]
      end

      def self.output
        []
      end

      def self.return_value
        :void
      end

      def self.authors
        ['Jerinji2016']
      end

      def self.is_supported?(platform)
        true
      end
    end
  end
end