module Fastlane
  module Actions
    module SharedValues
      FLUTTER_VERSION = :FLUTTER_VERSION
    end

    class GetPubspecVersionAction < Action
      def self.run(params)
        content = File.read(params[:path])
        require 'yaml'
        data = YAML.safe_load(content)

        version = data["version"]
        Actions.lane_context[SharedValues::FLUTTER_VERSION] = version

        UI.success "Fetched Version successfully #{version}"
        return version
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        'Gets the flutter project version from pubspec.yaml file'
      end

      def self.details
        'To update the version from pubspec.yaml, checkout `fastlane action update_pubspec_version`'
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(key: :path,
                                       env_name: 'FL_PUBSPEC_PATH',
                                       description: 'Path to your `pubspec.yaml` file',
                                       is_string: true,
                                       verify_block: proc do |value|
                                         unless value && !value.empty?
                                           UI.user_error!("Please pass path to pubspec.yaml file, pass using `path: '/path/to/pubspec.yaml'`")
                                         end
                                       end)
        ]
      end

      def self.output
        [
          ['FLUTTER_VERSION', 'Flutter Project version in pubspec.yaml'],
        ]
      end

      def self.return_value
        :string
      end

      def self.authors
        ['Jerinji2016']
      end

      def self.is_supported?(platform)
        true
      end
    end
  end
end