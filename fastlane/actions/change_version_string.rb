module Fastlane
  module Actions
    module SharedValues
      NEW_VERSION = :NEW_VERSION
    end

    class ChangeVersionStringAction < Action
      def self.run(params)
        version_string = params[:version]
        version, build_number = version_string.split('+')
        build_number = build_number.to_i
        major, minor, patch = version.split(".").map(&:to_i)

        if params[:patch]
          patch = params[:patch]
        elsif params[:bump_patch]
          patch += 1
        end

        if params[:minor]
          minor = params[:minor]
        elsif params[:bump_minor]
          minor += 1
        end

        if params[:major]
          major = params[:major]
        elsif params[:bump_major]
          major += 1
        end

        if params[:build_number]
          build_number = params[:build_number]
        elsif params[:bump_build_number]
          build_number += 1
        end

        version = "#{major}.#{minor}.#{patch}+#{build_number}"
        UI.success "Version Changed to #{version}"
        return version
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        'Change a version string of format `major.minor.patch+build_number`'
      end

      def self.details
        'Update the version of a provided string by bumping major, minor, patch or build number'
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(key: :version,
                                       env_name: 'FL_CURRENT_VERSION',
                                       description: 'Version that needs change',
                                       type: String,
                                       verify_block: proc do |value|
                                         unless value && !value.empty?
                                           UI.user_error!("No version was provided, pass using `version: '1.0.0+1'`")
                                         end
                                       end),

          FastlaneCore::ConfigItem.new(key: :patch,
                                       env_name: 'FL_VERSION_PATCH',
                                       description: 'Patch number to be replaced',
                                       type: Integer,
                                       optional: true),
          FastlaneCore::ConfigItem.new(key: :bump_patch,
                                       env_name: 'FL_BUMP_VERSION_PATCH',
                                       description: 'Increment patch by 1',
                                       type: Boolean,
                                       optional: true),

          FastlaneCore::ConfigItem.new(key: :minor,
                                       env_name: 'FL_VERSION_MINOR',
                                       description: 'Minor number to be replaced',
                                       type: Integer,
                                       optional: true),
          FastlaneCore::ConfigItem.new(key: :bump_minor,
                                       env_name: 'FL_BUMP_VERSION_MINOR',
                                       description: 'Increment minor by 1',
                                       type: Boolean,
                                       optional: true),

          FastlaneCore::ConfigItem.new(key: :major,
                                       env_name: 'FL_VERSION_MAJOR',
                                       description: 'Major number to be replaced',
                                       type: Integer,
                                       optional: true),
          FastlaneCore::ConfigItem.new(key: :bump_major,
                                       env_name: 'FL_BUMP_VERSION_MAJOR',
                                       description: 'Increment major by 1',
                                       type: Boolean,
                                       optional: true),

          FastlaneCore::ConfigItem.new(key: :build_number,
                                       env_name: 'FL_VERSION_BUILD_NUMBER',
                                       description: 'Build number to be replaced',
                                       type: Integer,
                                       optional: true),
          FastlaneCore::ConfigItem.new(key: :bump_build_number,
                                       env_name: 'FL_BUMP_VERSION_BUILD_NUMBER',
                                       description: 'Increment build number by 1',
                                       type: Boolean,
                                       optional: true)
        ]
      end

      def self.output
        [
          ['NEW_VERSION', 'Changed version'],
        ]
      end

      def self.return_value
        :string
      end

      def self.authors
        ['Jerinji2016']
      end

      def self.is_supported?(platform)
        true
      end
    end
  end
end