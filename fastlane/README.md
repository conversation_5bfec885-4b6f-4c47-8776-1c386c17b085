fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### code_gen

```sh
[bundle exec] fastlane code_gen
```

Run build_runner and easy_localization code generation commands

### release

```sh
[bundle exec] fastlane release
```

Build releases with shorebird integration

### build

```sh
[bundle exec] fastlane build
```

Build releases traditionally with Flutter

### post_release_cleanup

```sh
[bundle exec] fastlane post_release_cleanup
```

Post release cleanup
- Create git tag with version and changes in description
- Merge version branch into prod
- Create new release branch and check into branch
- Bump pubspec version
- Commit and push new branch changes

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
