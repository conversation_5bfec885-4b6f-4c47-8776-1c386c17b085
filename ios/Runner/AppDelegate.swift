import UIKit
import Flutter
import ScreenProtector<PERSON>it

@available(iOS 15.0, *)
@main
@objc class AppDelegate: FlutterAppDelegate {

    private var viewType: String = "arc-gis-map"
    private lazy var screenProtectorKit = { return ScreenProtectorKit(window: window) }()

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)

        screenProtectorKit.configurePreventionScreenshot()

        do {
            try setCacheConfig()
        } catch {
            print("setCacheConfig: something went wrong")
        }

        weak var registrar = self.registrar(forPlugin: "plugin-name")
        let factory = FLNativeViewFactory(messenger: registrar!.messenger())
        self.registrar(forPlugin: "<plugin-name>")!.register(
            factory,
            withId: viewType
        )
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    override func applicationDidBecomeActive(_ application: UIApplication) {
        screenProtectorKit.enabledPreventScreenshot()
    }

    override func applicationWillResignActive(_ application: UIApplication) {
        screenProtectorKit.disablePreventScreenshot()
    }

    func setCacheConfig() {
        URLCache.shared = URLCache(memoryCapacity: 0, diskCapacity: 0, diskPath: nil)
        URLSessionConfiguration.default.urlCache = URLCache(memoryCapacity: 0, diskCapacity: 0, diskPath: nil)
    }
}
