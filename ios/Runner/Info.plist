<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Bayaan Gov</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ar</string>
	</array>
	<key>CFBundleName</key>
	<string>adstatapp</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleTypeRole</key>
	<string>Editor</string>
	<key>CFBundleURLName</key>
	<string>scadmobile.page.link</string>
	<key>CFBundleURLSchemes</key>
	<array>
		<string>https</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>ae.gov.scad.adstatapp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>scadmobile.page.link</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>scad.gov.ae</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>scadoneapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>success</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>scadoneapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>failure</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>scadoneapp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FLTEnableImpeller</key>
	<false/>
	<key>FirebaseDeepLinkPasteboardRetrievalEnabled</key>
	<string>NO</string>
	<key>FirebaseDynamicLinksCustomDomains</key>
	<array>
		<string>https://scadmobile.page.link</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tel</string>
		<string>mailto</string>
		<string>uaepass</string>
		<string>uaepassqa</string>
		<string>uaepassdev</string>
		<string>uaepassstg</string>
		<string>scadmobile.page.link</string>
        <string>undecimus</string>
        <string>sileo</string>
        <string>zbra</string>
        <string>filza</string>
        <string>activator</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Allow access to the camera to let you take or choose a picture to upload and set it as your profile picture for your account.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow access to your photos so you can choose a picture to upload and set it as your profile picture for your account.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Authenticate to access the app features</string>

	<key>NSDocumentsFolderUsageDescription</key>
	<string>App wants to access your documents folder to download file</string>
	<key>NSDownloadsFolderUsageDescription</key>
	<string>App wants to access your downloads folder to download file</string>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>

	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarStyle</key>
	<string></string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
